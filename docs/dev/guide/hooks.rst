=====
Hooks
=====
..
  DO NOT EDIT THIS FILE MANUALLY. It is generated by: php lib/pkp/tools/getHooks.php -r

`API::_submissions::params`
    Class: :php:class:`PKP\API\v1\_submissions\PKPBackendSubmissionsController`
    
    Parameters: `[$collector, $illuminateRequest]`

`API::announcements::params`
    Class: :php:class:`PKP\API\v1\announcements\PKPAnnouncementController`
    
    Parameters: `[$collector, $illuminateRequest]`

`API::contexts::params`
    Class: :php:class:`PKP\API\v1\contexts\PKPContextController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::dois::params`
    Class: :php:class:`PKP\API\v1\dois\PKPDoiController`
    
    Parameters: `[[$collector, $illuminateRequest]]`

`API::emailTemplates::params`
    Class: :php:class:`PKP\API\v1\emailTemplates\PKPEmailTemplateController`
    
    Parameters: `[[$collector, $illuminateRequest]]`

`API::highlights::params`
    Class: :php:class:`PKP\API\v1\highlights\HighlightsController`
    
    Parameters: `[$collector, $illuminateRequest]`

`API::institutions::params`
    Class: :php:class:`PKP\API\v1\institutions\PKPInstitutionController`
    
    Parameters: `[[$collector, $illuminateRequest]]`

`API::payments::settings::edit`
    Class: :php:class:`PKP\API\v1\_payments\PKPBackendPaymentsSettingsController`
    
    Parameters: `[[ $illuminateRequest, $request, $params, $updatedSettings = new Collection(), $errors = new Collection() ]]`

`API::rors::params`
    Class: :php:class:`PKP\API\v1\rors\PKPRorController`
    
    Parameters: `[[$collector, $illuminateRequest]]`

`API::sections::params`
    Class: :php:class:`PKP\API\v1\sections\SectionController`
    
    Parameters: `[$collector, $illuminateRequest]`

`API::stats::context::params`
    Class: :php:class:`PKP\API\v1\stats\contexts\PKPStatsContextController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::context::timeline::params`
    Class: :php:class:`PKP\API\v1\stats\contexts\PKPStatsContextController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::contexts::params`
    Class: :php:class:`PKP\API\v1\stats\contexts\PKPStatsContextController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::contexts::timeline::params`
    Class: :php:class:`PKP\API\v1\stats\contexts\PKPStatsContextController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::editorial::averages::params`
    Class: :php:class:`PKP\API\v1\stats\editorial\PKPStatsEditorialController`
    
    Parameters: `[[&$params, $illuminateRequest]]`

`API::stats::editorial::params`
    Class: :php:class:`PKP\API\v1\stats\editorial\PKPStatsEditorialController`
    
    Parameters: `[[&$params, $illuminateRequest]]`

`API::stats::publication::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::publication::timeline::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::publications::cities::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::publications::countries::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::publications::files::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::publications::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::publications::regions::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::publications::timeline::params`
    Class: :php:class:`PKP\API\v1\stats\publications\PKPStatsPublicationController`
    
    Parameters: `[[&$allowedParams, $illuminateRequest]]`

`API::stats::users::params`
    Class: :php:class:`PKP\API\v1\stats\users\PKPStatsUserController`
    
    Parameters: `[[$collector, $illuminateRequest]]`

`API::submissions::params`
    Class: :php:class:`PKP\API\v1\submissions\PKPSubmissionController`
    
    Parameters: `[$collector, $illuminateRequest]`

`API::uploadPublicFile::permissions`
    Class: :php:class:`PKP\API\v1\_uploadPublicFile\PKPUploadPublicFileController`
    
    Parameters: `[[ &$userDir, &$isUserAllowed, &$allowedDirSize, &$allowedFileTypes, $request, $this->getAuthorizedContextObject(Application::ASSOC_TYPE_USER_ROLES), ]]`

`API::users::params`
    Class: :php:class:`PKP\API\v1\users\PKPUserController`
    
    Parameters: `[[&$params, $request]]`

`API::users::reviewers::params`
    Class: :php:class:`PKP\API\v1\users\PKPUserController`
    
    Parameters: `[[&$params, $request]]`

`API::users::user::report::params`
    Class: :php:class:`PKP\API\v1\users\PKPUserController`
    
    Parameters: `[[&$params, $request]]`

`API::vocabs::getMany`
    Class: :php:class:`PKP\API\v1\vocabs\PKPVocabController`
    
    Parameters: `[[$vocab, &$entries, $illuminateRequest, response(), $request]]`

`AboutContextHandler::editorialHistory`
    Class: :php:class:`PKP\pages\about\AboutContextHandler`
    
    Parameters: `[[$mastheadRoles, $mastheadUsers]]`

`AboutContextHandler::editorialMasthead`
    Class: :php:class:`PKP\pages\about\AboutContextHandler`
    
    Parameters: `[[$mastheadRoles, $mastheadUsers, $reviewers, $previousYear]]`

`Affiliation::validate`
    Class: :php:class:`PKP\affiliation\Repository
{
`
    
    Parameters: `[[$errors, $affiliation, $props, $submission, $context]]`

`Announcement::add`
    Class: :php:class:`PKP\announcement\Announcement`
    
    Parameters: `[[$this]]`

`Announcement::validate`
    Class: :php:class:`PKP\announcement\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props, $allowedLocales, $primaryLocale]]`

`Author::Collector`
    Class: :php:class:`PKP\author\Collector`
    
    Parameters: `[[&$q, $this]]`

`Author::add`
    Class: :php:class:`PKP\author\Repository
{
`
    
    Parameters: `[[$author]]`

`Author::add::before`
    Class: :php:class:`PKP\author\Repository
{
`
    
    Parameters: `[[$author]]`

`Author::delete::before`
    Class: :php:class:`PKP\author\Repository
{
`
    
    Parameters: `[[$author]]`

`Author::edit`
    Class: :php:class:`PKP\author\Repository
{
`
    
    Parameters: `[[$newAuthor, $author, $params]]`

`Author::newAuthorFromUser`
    Class: :php:class:`PKP\author\Repository
{
`
    
    Parameters: `[[$author, $user]]`

`Author::validate`
    Class: :php:class:`PKP\author\Repository
{
`
    
    Parameters: `[[$errors, $author, $props, $allowedLocales, $primaryLocale]]`

`Category::Collector`
    Class: :php:class:`PKP\category\Collector`
    
    Parameters: `[[&$qb, $this]]`

`Category::validate`
    Class: :php:class:`PKP\category\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props, $allowedLocales, $primaryLocale]]`

`Citation::importCitations::after`
    Class: :php:class:`PKP\citation\CitationDAO`
    
    Parameters: `[$publicationId, $existingCitations, $importedCitations]`

`CitationStyleLanguage::citation`
    Class: :php:class:`APP\plugins\generic\citationStyleLanguage\CitationStyleLanguagePlugin`
    
    Parameters: `[[&$citationData, &$citationStyle, $submission, $issue, $context, $publication]]`

`CitationStyleLanguage::citationDownloadDefaults`
    Class: :php:class:`APP\plugins\generic\citationStyleLanguage\CitationStyleLanguagePlugin`
    
    Parameters: `[[&$defaults, $this]]`

`CitationStyleLanguage::citationStyleDefaults`
    Class: :php:class:`APP\plugins\generic\citationStyleLanguage\CitationStyleLanguagePlugin`
    
    Parameters: `[[&$defaults, $this]]`

`Context::add`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$context, $request]]`

`Context::defaults::localeParams`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$localeParams, $context, $request]]`

`Context::delete::before`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$context]]`

`Context::edit`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$newContext, $context, $params, $request]]`

`Context::getContexts::queryObject`
    Class: :php:class:`PKP\services\queryBuilders\PKPContextQueryBuilder`
    
    Parameters: `[[&$q, $this]]`

`Context::getMany::queryBuilder`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$contextListQB, $args]]`

`Context::getProperties`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$values, $context, $props, $args]]`

`Context::restoreLocaleDefaults::localeParams`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$localeParams, $context, $request, $locale]]`

`Context::validate`
    Class: :php:class:`PKP\services\PKPContextService`
    
    Parameters: `[[&$errors, $action, $props, $allowedLocales, $primaryLocale]]`

`Dashboard::views`
    Class: :php:class:`PKP\pages\dashboard\DashboardPage:`
    
    Parameters: `[[&$views, $userRoles]]`

`Dc11SchemaPreprintAdapter::extractMetadataFromDataObject`
    Class: :php:class:`APP\plugins\metadata\dc11\filter\Dc11SchemaPreprintAdapter`
    
    Parameters: `[[$this, $submission, $server, &$dc11Description]]`

`Decision::Collector`
    Class: :php:class:`PKP\decision\Collector`
    
    Parameters: `[[&$qb, $this]]`

`Decision::add`
    Class: :php:class:`PKP\decision\Repository
{
`
    
    Parameters: `[[$decision]]`

`Decision::validate`
    Class: :php:class:`PKP\decision\Repository
{
`
    
    Parameters: `[[&$errors, $props]]`

`Dispatcher::dispatch`
    Class: :php:class:`PKP\core\Dispatcher
{
`
    
    Parameters: `[[$request]]`

`Doi::Collector`
    Class: :php:class:`PKP\doi\Collector`
    
    Parameters: `[[&$q, $this]]`

`Doi::markRegistered`
    Class: :php:class:`PKP\doi\Repository
{
`
    
    Parameters: `[[&$editParams]]`

`Doi::suffixValidation`
    Class: :php:class:`PKP\doi\Repository
{
`
    
    Parameters: `[[&$validRegexPattern]]`

`Doi::validate`
    Class: :php:class:`PKP\doi\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props]]`

`DoiListPanel::setConfig`
    Class: :php:class:`PKP\components\listPanels\PKPDoiListPanel`
    
    Parameters: `[[&$config]]`

`DoiSettingsForm::setEnabledRegistrationAgencies`
    Class: :php:class:`PKP\components\forms\context\PKPDoiRegistrationSettingsForm`
    
    Parameters: `[[&$registrationAgencies]]`

`DoisHandler::setListPanelArgs`
    Class: :php:class:`PKP\pages\dois\PKPDoisHandler`
    
    Parameters: `[[&$commonArgs]]`

`EditorAction::addReviewer`
    Class: :php:class:`PKP\submission\action\EditorAction
{
`
    
    Parameters: `[[&$submission, $reviewerId]]`

`EditorAction::clearReview`
    Class: :php:class:`PKP\controllers\grid\users\reviewer\form\UnassignReviewerForm`
    
    Parameters: `[[&$submission, $reviewAssignment]]`

`EditorAction::reinstateReview`
    Class: :php:class:`PKP\controllers\grid\users\reviewer\form\ReinstateReviewerForm`
    
    Parameters: `[[&$submission, $reviewAssignment]]`

`EditorAction::setDueDates`
    Class: :php:class:`PKP\submission\action\EditorAction
{
`
    
    Parameters: `[[&$reviewAssignment, &$reviewer, &$reviewDueDate, &$responseDueDate]]`

`EditorialStats::averages`
    Class: :php:class:`PKP\services\PKPStatsEditorialService
{
`
    
    Parameters: `[[&$averages, $args]]`

`EditorialStats::overview`
    Class: :php:class:`PKP\services\PKPStatsEditorialService
{
`
    
    Parameters: `[[&$overview, $args]]`

`EmailTemplate::Collector::custom`
    Class: :php:class:`PKP\emailTemplate\Collector`
    
    Parameters: `[[$q, $this]]`

`EmailTemplate::Collector::default`
    Class: :php:class:`PKP\emailTemplate\Collector`
    
    Parameters: `[[$q, $this]]`

`EmailTemplate::add`
    Class: :php:class:`PKP\emailTemplate\Repository
{
`
    
    Parameters: `[[$emailTemplate]]`

`EmailTemplate::restoreDefaults`
    Class: :php:class:`PKP\emailTemplate\Repository
{
`
    
    Parameters: `[[&$deletedKeys, $contextId]]`

`EmailTemplate::validate`
    Class: :php:class:`PKP\emailTemplate\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props, $allowedLocales, $primaryLocale]]`

`EventLog::Collector::getQueryBuilder`
    Class: :php:class:`PKP\log\event\Collector`
    
    Parameters: `[[&$q, $this]]`

`EventLog::validate`
    Class: :php:class:`PKP\log\event\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props, $allowedLocales, $primaryLocale]]`

`File::adapter`
    Class: :php:class:`PKP\services\PKPFileService
{
`
    
    Parameters: `[[&$adapter, $this]]`

`File::download`
    Class: :php:class:`PKP\services\PKPFileService
{
`
    
    Parameters: `[[$file, &$filename, $inline]]`

`File::formatFilename`
    Class: :php:class:`PKP\services\PKPFileService
{
`
    
    Parameters: `[[&$newFilename, $path, $filename]]`

`FileManager::deleteFile`
    Class: :php:class:`PKP\file\FileManager
{
`
    
    Parameters: `[[$filePath, &$result]]`

`FileManager::downloadFile`
    Class: :php:class:`PKP\file\FileManager
{
`
    
    Parameters: `[[&$filePath, &$mediaType, &$inline, &$result, &$fileName]]`

`FileManager::downloadFileFinished`
    Class: :php:class:`APP\pages\preprint\PreprintHandler`
    
    Parameters: `[[&$returner]]`

`Form::config::after`
    Class: :php:class:`PKP\components\forms\FormComponent
{
`
    
    Parameters: `[[&$config, $this]]`

`Form::config::before`
    Class: :php:class:`PKP\components\forms\FormComponent
{
`
    
    Parameters: `[$this]`

`Galley::getMany::queryObject`
    Class: :php:class:`APP\services\queryBuilders\GalleyQueryBuilder`
    
    Parameters: `[[&$q, $this]]`

`Galley::validate`
    Class: :php:class:`PKP\galley\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props, $allowedLocales, $primaryLocale]]`

`GenreDAO::_fromRow`
    Class: :php:class:`PKP\submission\GenreDAO`
    
    Parameters: `[[&$genre, &$row]]`

`Highlight::add`
    Class: :php:class:`PKP\highlight\Repository
{
`
    
    Parameters: `[$highlight]`

`Highlight::edit`
    Class: :php:class:`PKP\highlight\Repository
{
`
    
    Parameters: `[$newHighlight, $highlight, $params]`

`Highlight::validate`
    Class: :php:class:`PKP\highlight\Repository
{
`
    
    Parameters: `[&$errors, $object, $props, $context]`

`Installer::Installer`
    Class: :php:class:`PKP\install\Installer
{
`
    
    Parameters: `[[$this, &$descriptor, &$params]]`

`Installer::destroy`
    Class: :php:class:`PKP\install\Installer
{
`
    
    Parameters: `[[$this]]`

`Installer::executeInstaller`
    Class: :php:class:`PKP\install\Installer
{
`
    
    Parameters: `[[$this, &$result]]`

`Installer::parseInstaller`
    Class: :php:class:`PKP\install\Installer
{
`
    
    Parameters: `[[$this, &$result]]`

`Installer::postInstall`
    Class: :php:class:`PKP\install\Installer
{
`
    
    Parameters: `[[$this, &$result]]`

`Installer::preInstall`
    Class: :php:class:`PKP\install\Installer
{
`
    
    Parameters: `[[$this, &$result]]`

`Installer::updateVersion`
    Class: :php:class:`PKP\install\Installer
{
`
    
    Parameters: `[[$this, &$result]]`

`Institution::validate`
    Class: :php:class:`PKP\institution\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props, $allowedLocales, $primaryLocale]]`

`LibraryFileDAO::_fromRow`
    Class: :php:class:`PKP\context\LibraryFileDAO`
    
    Parameters: `[[&$libraryFile, &$row]]`

`LinkAction::construct`
    Class: :php:class:`PKP\linkAction\LinkAction
{
`
    
    Parameters: `[[$this]]`

`LoadComponentHandler`
    Class: :php:class:`PKP\core\PKPComponentRouter`
    
    Parameters: `[[&$component, &$op, &$componentInstance]]`

`LoadHandler`
    Class: :php:class:`PKP\core\PKPPageRouter`
    
    Parameters: `[[&$page, &$op, &$sourceFile, &$handler]]`

`Locale::installLocale`
    Class: :php:class:`PKP\i18n\Locale`
    
    Parameters: `[[&$locale]]`

`Locale::translate`
    Class: :php:class:`PKP\i18n\Locale`
    
    Parameters: `[[&$value, $key, $params, $number, $locale, $localeBundle]]`

`Mailer::Mailables`
    Class: :php:class:`PKP\mail\Repository
{
`
    
    Parameters: `[[$mailables, $context]]`

`NavigationMenus::displaySettings`
    Class: :php:class:`PKP\services\PKPNavigationMenuService
{
`
    
    Parameters: `[[$navigationMenuItem, $navigationMenu]]`

`NavigationMenus::itemCustomTemplates`
    Class: :php:class:`PKP\services\PKPNavigationMenuService
{
`
    
    Parameters: `[[&$templates]]`

`NavigationMenus::itemTypes`
    Class: :php:class:`PKP\services\PKPNavigationMenuService
{
`
    
    Parameters: `[[&$types]]`

`OAI::metadataFormats`
    Class: :php:class:`PKP\oai\OAI
{
`
    
    Parameters: `[[$namesOnly, $identifier, &$formats]]`

`OAIDAO::_returnIdentifierFromRow`
    Class: :php:class:`PKP\oai\PKPOAIDAO`
    
    Parameters: `[[&$record, &$row]]`

`OAIDAO::_returnRecordFromRow`
    Class: :php:class:`PKP\oai\PKPOAIDAO`
    
    Parameters: `[[&$record, &$row]]`

`OAIDAO::getServerSets`
    Class: :php:class:`APP\oai\ops\OAIDAO`
    
    Parameters: `[[$this, $serverId, $offset, $limit, $total, &$sets]]`

`PKPApplication::execute::catch`
    Class: :php:class:`PKP\core\iPKPApplicationInfoProvider
{
`
    
    Parameters: `['throwable' => $t]`

`PKPPageRouter::url`
    Class: :php:class:`PKP\core\PKPPageRouter`
    
    Parameters: `['request' => $request, 'newContext' => $newContext, 'page' => $page, 'op' => $op, 'path' => $path, 'params' => $params, 'anchor' => $anchor, 'escape' => $escape, 'urlLocaleForPage' => $urlLocaleForPage]`

`PageHandler::compileLess`
    Class: :php:class:`PKP\template\PKPTemplateManager`
    
    Parameters: `[[&$less, &$lessFile, &$args, $name, $request]]`

`PageHandler::displayCss`
    Class: :php:class:`PKP\controllers\page\PageHandler`
    
    Parameters: `[[$request, &$name, &$result, &$lastModified]]`

`PageHandler::getCompiledLess`
    Class: :php:class:`PKP\controllers\page\PageHandler`
    
    Parameters: `[[ 'request' => $request, 'name' => &$name, 'styles' => &$styles, ]]`

`PluginRegistry::getCategories`
    Class: :php:class:`PKP\plugins\PluginRegistry
{
`
    
    Parameters: `[[&$categories]]`

`PluginRegistry::loadCategory`
    Class: :php:class:`PKP\plugins\PluginRegistry
{
`
    
    Parameters: `[[&$category, &$plugins]]`

`PreprintHandler::download`
    Class: :php:class:`APP\pages\preprint\PreprintHandler`
    
    Parameters: `[[$this->preprint, &$this->galley, &$this->submissionFileId]]`

`PreprintHandler::view`
    Class: :php:class:`APP\pages\preprint\PreprintHandler`
    
    Parameters: `[[&$request, &$preprint, $publication]]`

`PreprintHandler::view::galley`
    Class: :php:class:`APP\pages\preprint\PreprintHandler`
    
    Parameters: `[[&$request, &$this->galley, &$preprint, $publication]]`

`PreprintSearch::getSimilarityTerms`
    Class: :php:class:`APP\search\PreprintSearch`
    
    Parameters: `[[$submissionId, &$searchTerms]]`

`PreprintSearchIndex::preprintChangesFinished`
    Class: :php:class:`APP\search\PreprintSearchIndex`
    
    Parameters: `[]`

`PreprintSearchIndex::preprintDeleted`
    Class: :php:class:`APP\search\PreprintSearchIndex`
    
    Parameters: `[[$preprintId]]`

`PreprintSearchIndex::preprintMetadataChanged`
    Class: :php:class:`APP\search\PreprintSearchIndex`
    
    Parameters: `[[$submission]]`

`PreprintSearchIndex::rebuildIndex`
    Class: :php:class:`APP\search\PreprintSearchIndex`
    
    Parameters: `[[$log, $server, $switches]]`

`PreprintSearchIndex::submissionFileChanged`
    Class: :php:class:`APP\search\PreprintSearchIndex`
    
    Parameters: `[[$preprintId, $type, $submissionFile->getId()]]`

`PreprintSearchIndex::submissionFileDeleted`
    Class: :php:class:`APP\search\PreprintSearchIndex`
    
    Parameters: `[[$preprintId, $type, $assocId]]`

`PreprintSearchIndex::submissionFilesChanged`
    Class: :php:class:`APP\search\PreprintSearchIndex`
    
    Parameters: `[[$preprint]]`

`Publication::Collector`
    Class: :php:class:`PKP\publication\Collector`
    
    Parameters: `[[&$qb, $this]]`

`Publication::canAuthorPublish`
    Class: :php:class:`APP\publication\Repository`
    
    Parameters: `[[$this]]`

`Publication::publish::before`
    Class: :php:class:`PKP\publication\Repository
{
`
    
    Parameters: `[[&$newPublication, $publication]]`

`Publication::unpublish::before`
    Class: :php:class:`PKP\publication\Repository
{
`
    
    Parameters: `[[ &$newPublication, $publication ]]`

`Publication::validate`
    Class: :php:class:`PKP\publication\Repository
{
`
    
    Parameters: `[[&$errors, $publication, $props, $allowedLocales, $primaryLocale]]`

`Publication::validatePublish`
    Class: :php:class:`PKP\publication\Repository
{
`
    
    Parameters: `[[&$errors, $publication, $submission, $allowedLocales, $primaryLocale]]`

`Publication::version`
    Class: :php:class:`PKP\publication\Repository
{
`
    
    Parameters: `[[&$newPublication, $publication]]`

`PublisherLibrary::types::names`
    Class: :php:class:`PKP\file\PKPLibraryFileManager`
    
    Parameters: `[[&$typeNameMap]]`

`PublisherLibrary::types::suffixes`
    Class: :php:class:`PKP\file\PKPLibraryFileManager`
    
    Parameters: `[[&$map]]`

`PublisherLibrary::types::titles`
    Class: :php:class:`PKP\file\PKPLibraryFileManager`
    
    Parameters: `[[&$map]]`

`Request::getBasePath`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$this->_basePath]]`

`Request::getBaseUrl`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$baseUrl]]`

`Request::getCompleteUrl`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$completeUrl]]`

`Request::getIndexUrl`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$indexUrl]]`

`Request::getProtocol`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$this->_protocol]]`

`Request::getQueryString`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$queryString]]`

`Request::getRemoteAddr`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$ipaddr]]`

`Request::getRemoteDomain`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$remoteDomain]]`

`Request::getRequestPath`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$this->_requestPath]]`

`Request::getRequestUrl`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$requestUrl]]`

`Request::getServerHost`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$this->_serverHost, &$default, &$includePort]]`

`Request::getUserAgent`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$this->_userAgent]]`

`Request::redirect`
    Class: :php:class:`PKP\core\PKPRequest
{
`
    
    Parameters: `[[&$url]]`

`RestrictedSiteAccessPolicy::_getLoginExemptions`
    Class: :php:class:`PKP\security\authorization\RestrictedSiteAccessPolicy`
    
    Parameters: `[[[&$exemptions]]]`

`ReviewAssignment::add`
    Class: :php:class:`PKP\submission\reviewAssignment\Repository
{
`
    
    Parameters: `[[$reviewAssignment]]`

`ReviewAssignment::delete::before`
    Class: :php:class:`PKP\submission\reviewAssignment\Repository
{
`
    
    Parameters: `[[$reviewAssignment]]`

`ReviewAssignment::edit`
    Class: :php:class:`PKP\submission\reviewAssignment\Repository
{
`
    
    Parameters: `[[$newReviewAssignment, $reviewAssignment, $params]]`

`ReviewAssignment::validate`
    Class: :php:class:`PKP\submission\reviewAssignment\Repository
{
`
    
    Parameters: `[&$errors, $object, $props, $allowedLocales, $primaryLocale]`

`ReviewFormDAO::_fromRow`
    Class: :php:class:`PKP\reviewForm\ReviewFormDAO`
    
    Parameters: `[[&$reviewForm, &$row]]`

`ReviewFormElementDAO::_fromRow`
    Class: :php:class:`PKP\reviewForm\ReviewFormElementDAO`
    
    Parameters: `[[&$reviewFormElement, &$row]]`

`ReviewFormResponseDAO::_returnReviewFormResponseFromRow`
    Class: :php:class:`PKP\reviewForm\ReviewFormResponseDAO`
    
    Parameters: `[[&$reviewFormResponse, &$row]]`

`ReviewerAction::confirmReview`
    Class: :php:class:`PKP\submission\reviewer\ReviewerAction
{
`
    
    Parameters: `[[$request, $submission, $mailable, $decline]]`

`Ror::validate`
    Class: :php:class:`PKP\ror\Repository
{
`
    
    Parameters: `[[$errors, $ror, $props]]`

`Router::getIndexUrl`
    Class: :php:class:`PKP\core\PKPRouter
{
`
    
    Parameters: `[[&$this->_indexUrl]]`

`Router::getRequestedContextPath`
    Class: :php:class:`PKP\core\PKPRouter
{
`
    
    Parameters: `[[&$this->_contextPath]]`

`Schema::get::`
    Class: :php:class:`PKP\services\PKPSchemaService
{
`
    
    Parameters: ``
    
    * @hook Schema::get::before::`

`Schema::get::(schemaName)`
    Class: :php:class:`PKP\services\PKPSchemaService
{
`
    
    Parameters: `[[schema]]`

`Schema::get::before::`
    Class: :php:class:`PKP\services\PKPSchemaService
{
`
    
    Parameters: ``
    
    * @hook Schema::get::before::`

`Section::validate`
    Class: :php:class:`PKP\section\Repository
{
`
    
    Parameters: `[[&$errors, $object, $props, $allowedLocales, $primaryLocale]]`

`ServerOAI::identifiers`
    Class: :php:class:`APP\oai\ops\ServerOAI`
    
    Parameters: `[[$this, $from, $until, $set, $offset, $limit, &$total, &$records]]`

`ServerOAI::records`
    Class: :php:class:`APP\oai\ops\ServerOAI`
    
    Parameters: `[[$this, $from, $until, $set, $offset, $limit, &$total, &$records]]`

`ServerOAI::sets`
    Class: :php:class:`APP\oai\ops\ServerOAI`
    
    Parameters: `[[$this, $offset, $limit, &$total, &$sets]]`

`Site::edit`
    Class: :php:class:`PKP\services\PKPSiteService`
    
    Parameters: `[[&$newSite, $site, $params, $request]]`

`Site::getProperties`
    Class: :php:class:`PKP\services\PKPSiteService`
    
    Parameters: `[[&$values, $site, $props, $args]]`

`Site::validate`
    Class: :php:class:`PKP\services\PKPSiteService`
    
    Parameters: `[[&$errors, $props, $allowedLocales, $primaryLocale]]`

`SitemapHandler::createServerSitemap`
    Class: :php:class:`APP\pages\sitemap\SitemapHandler`
    
    Parameters: `[[&$doc]]`

`Stats::editorial::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsEditorialService
{
`
    
    Parameters: `[[&$qb, $args]]`

`Stats::editorial::queryObject`
    Class: :php:class:`PKP\services\queryBuilders\PKPStatsEditorialQueryBuilder
{
`
    
    Parameters: `[[&$q, $this]]`

`Stats::getTimeline::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsServiceTrait
{
`
    
    Parameters: `[[&$timelineQB, $args]]`

`Stats::logUsageEvent`
    Class: :php:class:`PKP\observers\listeners\LogUsageEvent
{
`
    
    Parameters: `[[$usageEventLogEntry]]`

`StatsContext::queryObject`
    Class: :php:class:`PKP\services\queryBuilders\PKPStatsContextQueryBuilder`
    
    Parameters: `[[&$q, $this]]`

`StatsGeo::queryObject`
    Class: :php:class:`PKP\services\queryBuilders\PKPStatsGeoQueryBuilder`
    
    Parameters: `[[&$q, $this]]`

`StatsPublication::getCount::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsPublicationService
{
`
    
    Parameters: `[[&$metricsQB, $args]]`

`StatsPublication::getFilesCount::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsPublicationService
{
`
    
    Parameters: `[[&$metricsQB, $args]]`

`StatsPublication::getFilesTotals::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsPublicationService
{
`
    
    Parameters: `[[&$metricsQB, $args]]`

`StatsPublication::getTotals::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsPublicationService
{
`
    
    Parameters: `[[&$metricsQB, $args]]`

`StatsPublication::getTotalsByType::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsPublicationService
{
`
    
    Parameters: `[[&$metricsQB, $args]]`

`StatsPublication::queryBuilder`
    Class: :php:class:`PKP\services\PKPStatsPublicationService
{
`
    
    Parameters: `[[&$statsQB, $args]]`

`StatsPublication::queryObject`
    Class: :php:class:`PKP\services\queryBuilders\PKPStatsPublicationQueryBuilder`
    
    Parameters: `[[&$q, $this]]`

`StatsSushi::queryObject`
    Class: :php:class:`PKP\services\queryBuilders\PKPStatsSushiQueryBuilder`
    
    Parameters: `[[&$q, $this]]`

`Submission::Collector`
    Class: :php:class:`PKP\submission\Collector`
    
    Parameters: `[[&$q, $this]]`

`Submission::add`
    Class: :php:class:`PKP\submission\Repository
{
`
    
    Parameters: `[[$submission]]`

`Submission::getSubmissionsListProps`
    Class: :php:class:`PKP\submission\maps\Schema`
    
    Parameters: `[[&$props]]`

`Submission::updateStatus`
    Class: :php:class:`PKP\submission\Repository
{
`
    
    Parameters: `[[&$newStatus, $status, $submission]]`

`Submission::validate`
    Class: :php:class:`PKP\submission\Repository
{
`
    
    Parameters: `[[&$errors, $submission, $props, $allowedLocales, $primaryLocale]]`

`Submission::validateSubmit`
    Class: :php:class:`PKP\submission\Repository
{
`
    
    Parameters: `[[&$errors, $submission, $context]]`

`SubmissionCommentDAO::_fromRow`
    Class: :php:class:`PKP\submission\SubmissionCommentDAO`
    
    Parameters: `[[&$submissionComment, &$row]]`

`SubmissionFile::Collector::getQueryBuilder`
    Class: :php:class:`PKP\submissionFile\Collector`
    
    Parameters: `[[&$qb, $this]]`

`SubmissionFile::supportsDependentFiles`
    Class: :php:class:`PKP\submissionFile\Repository
{
`
    
    Parameters: `[[&$result, $submissionFile]]`

`SubmissionFile::validate`
    Class: :php:class:`PKP\submissionFile\Repository
{
`
    
    Parameters: `[[ &$errors, $object, $props, $allowedLocales, $primaryLocale ]]`

`SubmissionSearch::getResultSetOrderingOptions`
    Class: :php:class:`APP\search\PreprintSearch`
    
    Parameters: `[[$context, &$resultSetOrderingOptions]]`

`SubmissionSearch::retrieveResults`
    Class: :php:class:`PKP\search\SubmissionSearch
{
`
    
    Parameters: `[[&$context, &$keywords, $publishedFrom, $publishedTo, $orderBy, $orderDir, $exclude, $page, $itemsPerPage, &$totalResults, &$error, &$results]]`

`TemplateManager::display`
    Class: :php:class:`PKP\template\PKPTemplateManager`
    
    Parameters: `[[$this, &$template, &$output]]`

`TemplateManager::fetch`
    Class: :php:class:`PKP\template\PKPTemplateManager`
    
    Parameters: `[[$this, $template, $cache_id, $compile_id, &$result]]`

`TemplateManager::setupBackendPage`
    Class: :php:class:`PKP\template\PKPTemplateManager`
    
    Parameters: `[]`

`TemplateResource::getFilename`
    Class: :php:class:`PKP\template\PKPTemplateResource`
    
    Parameters: `[[&$filePath, $template]]`

`TemporaryFileDAO::_returnTemporaryFileFromRow`
    Class: :php:class:`PKP\file\TemporaryFileDAO`
    
    Parameters: `[[&$temporaryFile, &$row]]`

`ThankReviewerForm::thankReviewer`
    Class: :php:class:`PKP\controllers\grid\users\reviewer\form\ThankReviewerForm`
    
    Parameters: `[[$submission, $reviewAssignment, $mailable]]`

`UsageEventPlugin::getUsageEvent`
    Class: :php:class:`PKP\plugins\generic\usageEvent\PKPUsageEventPlugin`
    
    Parameters: `[$hookName, $usageEvent, ...]`

`User::Collector`
    Class: :php:class:`PKP\user\Collector`
    
    Parameters: `[[$query, $this]]`

`User::getReport`
    Class: :php:class:`PKP\user\Repository
{
`
    
    Parameters: `[[$report]]`

`UserAction::mergeUsers`
    Class: :php:class:`PKP\user\Repository
{
`
    
    Parameters: `[[&$oldUserId, &$newUserId]]`

`UserGroup::add`
    Class: :php:class:`PKP\userGroup\UserGroup`
    
    Parameters: `[[$this]]`

`UserGroup::delete::before`
    Class: :php:class:`PKP\userGroup\UserGroup`
    
    Parameters: `[[$userGroup]]`

`UserGroup::edit`
    Class: :php:class:`PKP\userGroup\UserGroup`
    
    Parameters: `[[$this]]`

`UserGroup::validate`
    Class: :php:class:`PKP\userGroup\Repository
{
`
    
    Parameters: `[[$errors, $userGroup, $props, $allowedLocales, $primaryLocale]]`

`UserSchema::getProperties::values`
    Class: :php:class:`PKP\user\maps\Schema`
    
    Parameters: `[[$this, &$output, $user, $props]]`

`VersionDAO::_returnVersionFromRow`
    Class: :php:class:`PKP\site\VersionDAO`
    
    Parameters: `[[&$version, &$row]]`

`Workflow::Decisions`
    Class: :php:class:`APP\submission\maps\Schema`
    
    Parameters: `[[&$decisionTypes, $stageId]]`

