OPS Release Procedure
---------------------

The following steps (subject to change) are typically performed to prepare a new
OPS release:


1. Update version, installation, and upgrade descriptor files:

    dbscripts/xml/version.xml
    dbscripts/xml/install.xml
    dbscripts/xml/upgrade.xml
    package.json (and package-lock.json, using `npm install`)
    lib/ui-library/package.json (and lib/ui-library/package-lock.json, using `npm install`)

    Commands that may be helpful in determining what bugs have been closed
    since the last release (whose tag is specified in place of <last-tag>:

    git log --pretty=oneline <last-tag>..HEAD # Generate a log from the last release
        | egrep -o "(#|\*)([0-9]+)(#|\*)"     # Look for bug IDs in the commit notes
        | sed -r "s/(#|\*)//g"                # Remove the octothorpes
        | sort -nu                            # Get a unique sorted list
        > bug_ids.txt                         # Dump to a file

    The titles of these bugs can be fetched as follows:

    for id in `cat bug_ids.txt`; do
        wget -O - "https://github.com/pkp/pkp-lib/issues/$id"
        | grep -o -e "<title>.*<\/title>"
    ; done

    This can be done both for the application and for the PKP repository to
    ensure that all relevant commits (and all related bugs) are noted in the
    release notes.


2. Update release documentation:

    docs/release-notes/README-<VERSION>
    php lib/pkp/tools/getHooks.php -r > docs/dev/guide/hooks.rst


3. After committing any pending modifications, tag the release in git (both the
OPS codebase and the PKP library to be distributed with it). DO NOT FORGET TO
COMMIT THE lib/pkp HASH FIRST.

WARNING: If the release is to have minified JS, set the default in
config.TEMPLATE.inc.php and compile and commit the latest JS before tagging!

    cd lib/pkp
    git tag ops-<MAJOR>_<MINOR>_<REVISION>-<BUILD>
    git push --tags
    cd ../..
    git tag ops-<MAJOR>_<MINOR>_<REVISION>-<BUILD>
    git push --tags


4. Build the OPS package and patch files:

WARNING: If working on a stable branch, the latest `js/build.js` file will be
compiled when `tools/buildpkg.sh` is run. Remove js/build.js from the .gitignore
file and commit it to the new stable branch.

    sh tools/buildpkg.sh <VERSION> <TAG>-<BRANCH> <PATCH_DIR>

    (<PATCH_DIR> is the path to a directory containing previous OPS release
    packages against which to build patches)


5. Place the new package, patches, version descriptor, and Doxygen docs on the
    OPS web site:

    https://pkp.sfu.ca/ops/ops_download/
    https://pkp.sfu.ca/ops/download/patch/
    https://pkp.sfu.ca/ops/xml/ops-version.xml
    https://pkp.sfu.ca/ops/doxygen/master/html/

6. Place the release notes, upgrade instructions, readme, and any relevant DTDs
    on the OPS website:

    https://pkp.sfu.ca/ops/README-<VERSION>
    https://pkp.sfu.ca/ops/RELEASE-<VERSION>
    https://pkp.sfu.ca/ops/UPGRADE-<VERSION>
    https://pkp.sfu.ca/ops/dtds

7. If this is a major release, update the Release Notebook to include the release
    date. Example:

    OJS, OMP and OPS version 3.4 was released on DD, Month, YYYY.

8. When a stable branch is created for a major release, update the Admin Guide
    with a permalink to the sample XML import file that points to the file in
    the stable branch. Example:

    https://github.com/pkp/pkp-docs/blob/325461a5b7dcefc1477f18a25ca84027e38b302a/admin-guide/en/data-import-and-export.md?plain=1#L39-L42
