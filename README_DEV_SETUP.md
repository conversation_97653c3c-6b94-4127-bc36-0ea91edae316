# OPS开发环境快速设置

本目录包含了完整的OPS (Open Preprint Systems) 开发环境设置脚本和文档。

## 📁 文件说明

| 文件名 | 用途 | 描述 |
|--------|------|------|
| `setup-ops-dev.sh` | 🚀 自动安装 | 一键安装完整的OPS开发环境 |
| `start-ops-dev.sh` | ▶️ 启动服务 | 快速启动所有开发服务 |
| `stop-ops-dev.sh` | ⏹️ 停止服务 | 停止所有开发服务 |
| `restart-ops-dev.sh` | 🔄 重启服务 | 重启所有开发服务 |
| `verify-ops-setup.sh` | ✅ 验证环境 | 检查环境是否正确配置 |
| `OPS_DEVELOPMENT_SETUP.md` | 📖 详细文档 | 完整的安装和配置指南 |

## 🚀 快速开始

### 新环境安装

如果您是第一次设置OPS开发环境：

```bash
# 1. 运行自动安装脚本
./setup-ops-dev.sh

# 2. 验证安装
./verify-ops-setup.sh
```

### 已有环境使用

如果您已经安装过OPS开发环境：

```bash
# 启动所有服务
./start-ops-dev.sh

# 停止所有服务
./stop-ops-dev.sh

# 重启所有服务
./restart-ops-dev.sh
```

## 🌐 访问地址

安装完成后，您可以通过以下地址访问：

- **OPS主站**: http://localhost:8000
- **pgAdmin数据库管理**: http://localhost:8080

## 🔑 默认登录信息

### 数据库连接
- **主机**: localhost
- **端口**: 5432
- **数据库**: ops
- **用户名**: ops
- **密码**: ops_password

### pgAdmin登录
- **邮箱**: <EMAIL>
- **密码**: admin

## 📋 环境要求

- **操作系统**: Ubuntu 22.04 LTS
- **PHP**: 8.3.x
- **Docker**: 最新版本
- **Docker Compose**: 最新版本
- **Composer**: 2.x
- **内存**: 至少 4GB RAM
- **存储**: 至少 10GB 可用空间

## 🛠️ 常用命令

```bash
# 检查环境状态
./verify-ops-setup.sh

# 查看服务器日志
tail -f ops-server.log

# 重新构建前端资源
npm run build

# 查看Docker容器状态
docker ps | grep ops

# 备份数据库
./docker/postgres-setup.sh backup

# 恢复数据库
./docker/postgres-setup.sh restore backup_file.sql
```

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep :8000
   sudo netstat -tlnp | grep :5432
   ```

2. **Docker容器启动失败**
   ```bash
   # 重启Docker服务
   sudo systemctl restart docker
   
   # 清理Docker资源
   docker system prune -f
   ```

3. **PHP扩展缺失**
   ```bash
   # 重新安装PHP扩展
   sudo apt install php8.3-pgsql php8.3-mbstring php8.3-xml
   ```

4. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x *.sh
   ```

### 获取帮助

1. 查看详细文档: `cat OPS_DEVELOPMENT_SETUP.md`
2. 运行环境验证: `./verify-ops-setup.sh`
3. 检查日志文件: `tail -f ops-server.log`

## 📚 相关文档

- [PKP开发文档](https://docs.pkp.sfu.ca/dev/)
- [OPS用户指南](https://docs.pkp.sfu.ca/ops/)
- [Docker文档](https://docs.docker.com/)

## 🤝 贡献

如果您发现问题或有改进建议，请：

1. 检查 `OPS_DEVELOPMENT_SETUP.md` 中的故障排除部分
2. 运行 `./verify-ops-setup.sh` 诊断问题
3. 查看相关日志文件

---

**最后更新**: 2025-07-27  
**适用版本**: OPS 3.5.0, Ubuntu 22.04, PHP 8.3

## 🎯 下一步

1. 运行 `./setup-ops-dev.sh` 开始安装
2. 访问 http://localhost:8000 完成OPS配置
3. 开始您的开发工作！
