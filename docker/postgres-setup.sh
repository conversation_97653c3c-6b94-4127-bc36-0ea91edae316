#!/bin/bash

# PostgreSQL Docker 设置和管理脚本
# 用于 Open Preprint Systems (OPS)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    print_header "检查系统要求"
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装。请先安装 Docker。"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装。请先安装 Docker Compose。"
        exit 1
    fi
    
    print_message "Docker 和 Docker Compose 已安装"
}

# 启动 PostgreSQL 容器
start_postgres() {
    print_header "启动 PostgreSQL 容器"
    
    if [ ! -f ".env" ]; then
        print_warning ".env 文件不存在，复制 .env.example"
        cp .env.example .env
    fi
    
    # 使用 docker compose 或 docker-compose
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    print_message "启动 PostgreSQL 和 pgAdmin 容器..."
    $COMPOSE_CMD up -d postgres pgadmin
    
    print_message "等待 PostgreSQL 启动..."
    sleep 10
    
    # 检查容器状态
    if $COMPOSE_CMD ps postgres | grep -q "Up"; then
        print_message "PostgreSQL 容器启动成功！"
        print_message "数据库连接信息："
        echo "  主机: localhost"
        echo "  端口: 5432"
        echo "  数据库: ops"
        echo "  用户名: ops"
        echo "  密码: ops_password"
        echo ""
        print_message "pgAdmin 访问信息："
        echo "  URL: http://localhost:8080"
        echo "  邮箱: <EMAIL>"
        echo "  密码: admin"
    else
        print_error "PostgreSQL 容器启动失败"
        $COMPOSE_CMD logs postgres
        exit 1
    fi
}

# 停止容器
stop_postgres() {
    print_header "停止 PostgreSQL 容器"
    
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    $COMPOSE_CMD down
    print_message "PostgreSQL 容器已停止"
}

# 重启容器
restart_postgres() {
    print_header "重启 PostgreSQL 容器"
    stop_postgres
    start_postgres
}

# 查看日志
show_logs() {
    print_header "PostgreSQL 容器日志"
    
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    $COMPOSE_CMD logs -f postgres
}

# 进入 PostgreSQL 容器
enter_postgres() {
    print_header "进入 PostgreSQL 容器"
    docker exec -it ops_postgres psql -U ops -d ops
}

# 备份数据库
backup_database() {
    print_header "备份数据库"
    
    BACKUP_DIR="./backups"
    mkdir -p $BACKUP_DIR
    
    BACKUP_FILE="$BACKUP_DIR/ops_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    print_message "创建数据库备份: $BACKUP_FILE"
    docker exec ops_postgres pg_dump -U ops -d ops > $BACKUP_FILE
    
    if [ $? -eq 0 ]; then
        print_message "备份成功创建: $BACKUP_FILE"
    else
        print_error "备份失败"
        exit 1
    fi
}

# 恢复数据库
restore_database() {
    if [ -z "$1" ]; then
        print_error "请提供备份文件路径"
        echo "用法: $0 restore <backup_file>"
        exit 1
    fi
    
    BACKUP_FILE="$1"
    
    if [ ! -f "$BACKUP_FILE" ]; then
        print_error "备份文件不存在: $BACKUP_FILE"
        exit 1
    fi
    
    print_header "恢复数据库"
    print_warning "这将覆盖现有数据库内容！"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message "从备份恢复数据库: $BACKUP_FILE"
        docker exec -i ops_postgres psql -U ops -d ops < $BACKUP_FILE
        
        if [ $? -eq 0 ]; then
            print_message "数据库恢复成功"
        else
            print_error "数据库恢复失败"
            exit 1
        fi
    else
        print_message "操作已取消"
    fi
}

# 显示帮助信息
show_help() {
    echo "PostgreSQL Docker 管理脚本"
    echo ""
    echo "用法: $0 [命令]"
    echo ""
    echo "命令:"
    echo "  start     启动 PostgreSQL 容器"
    echo "  stop      停止 PostgreSQL 容器"
    echo "  restart   重启 PostgreSQL 容器"
    echo "  logs      查看 PostgreSQL 日志"
    echo "  shell     进入 PostgreSQL 容器"
    echo "  backup    备份数据库"
    echo "  restore   恢复数据库 (需要备份文件路径)"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start"
    echo "  $0 backup"
    echo "  $0 restore ./backups/ops_backup_20240127_120000.sql"
}

# 主函数
main() {
    case "${1:-help}" in
        "start")
            check_requirements
            start_postgres
            ;;
        "stop")
            stop_postgres
            ;;
        "restart")
            check_requirements
            restart_postgres
            ;;
        "logs")
            show_logs
            ;;
        "shell")
            enter_postgres
            ;;
        "backup")
            backup_database
            ;;
        "restore")
            restore_database "$2"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 运行主函数
main "$@"
