# PostgreSQL 14 Docker 配置指南

本指南将帮助您为 Open Preprint Systems (OPS) 设置 PostgreSQL 14 Docker 容器。

## 📋 系统要求

- Ubuntu 22.04
- PHP 8.3 (已安装)
- Docker 和 Docker Compose
- 至少 2GB 可用磁盘空间

## 🚀 快速开始

### 1. 安装 Docker 和 Docker Compose (如果尚未安装)

```bash
# 更新包列表
sudo apt update

# 安装 Docker
sudo apt install -y docker.io

# 启动并启用 Docker 服务
sudo systemctl start docker
sudo systemctl enable docker

# 将当前用户添加到 docker 组
sudo usermod -aG docker $USER

# 安装 Docker Compose
sudo apt install -y docker-compose

# 重新登录以应用组更改
newgrp docker
```

### 2. 启动 PostgreSQL 容器

```bash
# 使用管理脚本启动
./docker/postgres-setup.sh start

# 或者直接使用 Docker Compose
docker-compose up -d postgres pgadmin
```

### 3. 验证安装

```bash
# 检查容器状态
docker-compose ps

# 查看 PostgreSQL 日志
docker-compose logs postgres
```

## 🔧 配置 OPS

### 1. 创建配置文件

```bash
# 复制示例配置文件
cp config.inc.php.example config.inc.php
```

### 2. 编辑配置文件

编辑 `config.inc.php` 文件中的数据库设置：

```php
[database]
driver = postgres
host = localhost  # 或者 postgres (如果 OPS 也在 Docker 中)
port = 5432
username = ops
password = ops_password
name = ops
```

### 3. 安装 PHP PostgreSQL 扩展

```bash
# 安装 PHP PostgreSQL 扩展
sudo apt install -y php8.3-pgsql php8.3-mbstring php8.3-xml php8.3-curl

# 重启 Web 服务器 (如果使用 Apache)
sudo systemctl restart apache2

# 或者重启 PHP-FPM (如果使用 Nginx)
sudo systemctl restart php8.3-fpm
```

## 📊 数据库管理

### 连接信息

- **主机**: localhost
- **端口**: 5432
- **数据库**: ops
- **用户名**: ops
- **密码**: ops_password

### pgAdmin Web 界面

- **URL**: http://localhost:8080
- **邮箱**: <EMAIL>
- **密码**: admin

### 命令行访问

```bash
# 进入 PostgreSQL 容器
./docker/postgres-setup.sh shell

# 或者直接使用 Docker
docker exec -it ops_postgres psql -U ops -d ops
```

## 🛠️ 管理脚本使用

管理脚本 `docker/postgres-setup.sh` 提供了以下功能：

```bash
# 启动容器
./docker/postgres-setup.sh start

# 停止容器
./docker/postgres-setup.sh stop

# 重启容器
./docker/postgres-setup.sh restart

# 查看日志
./docker/postgres-setup.sh logs

# 进入数据库 shell
./docker/postgres-setup.sh shell

# 备份数据库
./docker/postgres-setup.sh backup

# 恢复数据库
./docker/postgres-setup.sh restore /path/to/backup.sql

# 显示帮助
./docker/postgres-setup.sh help
```

## 💾 数据持久化

数据库数据存储在 Docker 卷中，即使容器被删除，数据也会保留：

- **postgres_data**: PostgreSQL 数据文件
- **pgadmin_data**: pgAdmin 配置和设置

## 🔒 安全建议

### 生产环境配置

1. **更改默认密码**：
   ```bash
   # 编辑 .env 文件
   POSTGRES_PASSWORD=your_secure_password_here
   PGADMIN_DEFAULT_PASSWORD=your_admin_password_here
   ```

2. **限制网络访问**：
   ```yaml
   # 在 docker-compose.yml 中移除端口映射
   # ports:
   #   - "5432:5432"
   ```

3. **使用 SSL 连接**：
   ```php
   # 在 config.inc.php 中启用 SSL
   force_ssl = On
   ```

## 🐛 故障排除

### 常见问题

1. **容器启动失败**：
   ```bash
   # 查看详细日志
   docker-compose logs postgres
   
   # 检查端口是否被占用
   sudo netstat -tlnp | grep 5432
   ```

2. **连接被拒绝**：
   ```bash
   # 确保容器正在运行
   docker-compose ps
   
   # 检查防火墙设置
   sudo ufw status
   ```

3. **权限问题**：
   ```bash
   # 确保用户在 docker 组中
   groups $USER
   
   # 如果不在，添加用户到 docker 组
   sudo usermod -aG docker $USER
   newgrp docker
   ```

### 重置数据库

如果需要完全重置数据库：

```bash
# 停止容器
docker-compose down

# 删除数据卷
docker volume rm ops_postgres_data

# 重新启动
docker-compose up -d postgres
```

## 📈 性能优化

### PostgreSQL 配置调优

编辑 `docker/postgres/postgresql.conf` (如果需要)：

```conf
# 内存设置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB

# 连接设置
max_connections = 100

# 日志设置
log_min_duration_statement = 1000
```

### 监控

```bash
# 查看数据库大小
docker exec ops_postgres psql -U ops -d ops -c "SELECT pg_size_pretty(pg_database_size('ops'));"

# 查看活动连接
docker exec ops_postgres psql -U ops -d ops -c "SELECT * FROM pg_stat_activity;"
```

## 📚 相关文档

- [PostgreSQL 官方文档](https://www.postgresql.org/docs/14/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [OPS 官方文档](https://docs.pkp.sfu.ca/)

## 🆘 获取帮助

如果遇到问题，请检查：

1. Docker 和 Docker Compose 版本兼容性
2. 系统资源使用情况
3. 网络端口冲突
4. 文件权限设置

需要更多帮助，请查看 OPS 社区论坛或提交 issue。
