---
name: Accessibility Issue
about: An issue related exclusively to accessibility
title: ''
labels: Accessibility
assignees: ''

---

## Issue Description
// What is the nature of the issue? How does it impact users?


## How to reproduce / Current behavior
// Step-by-step instructions explaining how to reproduce the problem. The environment required, explicit identification of the component in question


## What application are you using?
 OJS, OMP or OPS version X.X.X

## Stack used 
- Device: xyz
- OS: xyz
- Browser: xyz
- Version: xyz

## Testing method
- Method: (e.g. Manual; Keyboard)
- Automated test: (e.g. tool name)

## Action
// Describe action that can be taken to remediate the issue. 


## Additional information
// Please add any screenshots, code snippets, or other information we can use to investigate this issue.
