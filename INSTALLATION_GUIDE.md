# OPS 开发环境安装指南

基于PKP官方文档和当前环境配置的完整安装步骤。

## 📋 当前环境状态

✅ Ubuntu 22.04  
✅ PHP 8.3.23 已安装  
✅ PostgreSQL 14 Docker 容器已配置  
❌ Composer 未安装  
❌ Node.js/NPM 未安装  

## 🚀 安装步骤

### 第一步：安装 Composer

```bash
# 方法1：使用 apt 安装（推荐，简单快速）
sudo apt update
sudo apt install -y composer

# 方法2：官方安装脚本（获取最新版本）
# curl -sS https://getcomposer.org/installer | php
# sudo mv composer.phar /usr/local/bin/composer
# sudo chmod +x /usr/local/bin/composer
```

### 第二步：安装 Node.js 和 NPM

```bash
# 安装 Node.js 18.x LTS（推荐版本）
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 验证安装
node --version
npm --version
```

### 第三步：安装必要的 PHP 扩展

```bash
# 安装 OPS 所需的 PHP 扩展
sudo apt install -y \
    php8.3-pgsql \
    php8.3-mbstring \
    php8.3-xml \
    php8.3-curl \
    php8.3-gd \
    php8.3-zip \
    php8.3-intl \
    php8.3-bcmath \
    php8.3-dom \
    php8.3-fileinfo \
    php8.3-filter \
    php8.3-hash \
    php8.3-openssl \
    php8.3-session \
    php8.3-simplexml \
    php8.3-tokenizer \
    php8.3-xmlwriter \
    php8.3-zlib

# 重启 PHP 服务（如果使用 Apache 或 Nginx）
sudo systemctl restart apache2 || sudo systemctl restart nginx || true
```

### 第四步：配置 OPS

```bash
# 1. 复制配置文件模板
cp config.TEMPLATE.inc.php config.inc.php

# 2. 编辑配置文件（将在下一步提供具体配置）
# nano config.inc.php
```

### 第五步：安装 Composer 依赖

```bash
# 安装核心依赖
composer --working-dir=lib/pkp install

# 安装引用样式语言插件依赖
composer --working-dir=plugins/generic/citationStyleLanguage install

# 注意：OPS 不需要 PayPal 插件，这是 OJS/OMP 特有的
```

### 第六步：安装和构建前端依赖

```bash
# 安装 NPM 依赖
npm install

# 构建前端资源
npm run build
```

### 第七步：启动 PostgreSQL 容器

```bash
# 启动数据库容器
./docker/postgres-setup.sh start

# 验证容器运行状态
docker-compose ps
```

### 第八步：启动 OPS 开发服务器

```bash
# 使用 PHP 内置服务器启动应用
php -S localhost:8000
```

### 第九步：通过浏览器完成安装

1. 打开浏览器访问：http://localhost:8000
2. 按照安装向导完成配置
3. 使用以下数据库连接信息：
   - 数据库类型：PostgreSQL
   - 主机：localhost
   - 端口：5432
   - 数据库名：ops
   - 用户名：ops
   - 密码：ops_password

## 🔧 配置文件详细设置

### config.inc.php 关键配置

```php
[general]
base_url = "http://localhost:8000"
session_cookie_name = ops_session
disable_path_info = Off
restful_urls = On

[database]
driver = postgres
host = localhost
port = 5432
username = ops
password = ops_password
name = ops
connection_charset = utf8
debug = Off

[email]
default = log

[security]
force_ssl = Off
force_login_ssl = Off
session_check_ip = On

[files]
files_dir = files
public_files_dir = public

[i18n]
locale = en
connection_charset = utf8
```

## 🛠️ 故障排除

### 常见问题及解决方案

1. **Composer 内存不足**：
```bash
php -d memory_limit=-1 /usr/bin/composer --working-dir=lib/pkp install
```

2. **NPM 权限问题**：
```bash
sudo chown -R $USER:$USER ~/.npm
```

3. **PHP 扩展缺失**：
```bash
php -m | grep -E "(pgsql|mbstring|xml|curl|gd|zip)"
```

4. **数据库连接失败**：
```bash
# 检查 PostgreSQL 容器状态
docker-compose logs postgres
```

## 📝 验证安装

### 检查所有组件

```bash
# 检查 PHP 版本和扩展
php --version
php -m | grep pgsql

# 检查 Composer
composer --version

# 检查 Node.js 和 NPM
node --version
npm --version

# 检查数据库连接
./docker/postgres-setup.sh shell
# 在 PostgreSQL shell 中运行：\l
```

## 🔄 开发工作流

### 日常开发命令

```bash
# 启动开发环境
./docker/postgres-setup.sh start
php -S localhost:8000

# 更新依赖
composer --working-dir=lib/pkp update
npm install
npm run build

# 数据库升级（如有需要）
php tools/upgrade.php upgrade

# 查看日志
./docker/postgres-setup.sh logs
```

## 📚 下一步

安装完成后，您可以：

1. 访问 http://localhost:8000 完成 OPS 初始配置
2. 访问 http://localhost:8080 使用 pgAdmin 管理数据库
3. 阅读 [OPS 架构文档](https://docs.pkp.sfu.ca/dev/documentation/en/architecture)
4. 开始开发自定义功能或插件

## 🆘 获取帮助

如果遇到问题：
1. 查看 `docker/README.md` 中的故障排除部分
2. 检查 PKP 社区论坛：https://forum.pkp.sfu.ca
3. 查看官方文档：https://docs.pkp.sfu.ca/
