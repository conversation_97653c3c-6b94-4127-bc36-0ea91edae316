/**
 * @file cypress/tests/data/60-content/FpaglieriSubmission.cy.js
 *
 * Copyright (c) 2014-2021 Simon <PERSON>
 * Copyright (c) 2000-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 */

describe('Data suite: Fpaglieri', function() {
	let submission;

	before(function() {
		const title = 'Hansen & Pinto: Reason Reclaimed';
		submission = {
			id: 0,
			section: 'Preprints',
			prefix: '',
			title: title,
			subtitle: '',
			abstract: 'None.',
			shortAuthorString: 'Paglieri',
			authorNames: ['Fabio Paglieri'],
			sectionId: 1,
			assignedAuthorNames: ['Fabio Paglieri'],
			files: [
				{
					'file': 'dummy.pdf',
					'fileName': title + '.pdf',
					'mimeType': 'application/pdf',
					'genre': Cypress.env('defaultGenre')
				},
			]
		};
	});

	it('Create a submission', function() {
		cy.register({
			'username': 'fpagli<PERSON>',
			'givenName': 'Fabio',
			'familyName': 'Pa<PERSON><PERSON>',
			'affiliation': 'University of Rome',
			'country': 'Italy',
		});

		cy.getCsrfToken();
		cy.window()
			.then(() => {
				return cy.createSubmissionWithApi(submission, this.csrfToken);
			})
			.then(xhr => {
				return cy.submitSubmissionWithApi(submission.id, this.csrfToken);
			});

		cy.logout();
		cy.findSubmissionAsEditor('dbarnes', null, 'Paglieri');
		cy.get('button:contains("Post the preprint")').click();
		cy.get('button:contains("Post"):visible').click();
		cy.get('div:contains("All requirements have been met. Are you sure you want to post this?")');
		cy.get('[id^="publish"] button:contains("Post")').click();
	});
});
