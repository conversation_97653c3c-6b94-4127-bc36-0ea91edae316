/**
 * @file cypress/tests/data/60-content/ZwoodsSubmission.cy.js
 *
 * Copyright (c) 2014-2021 Simon <PERSON> University
 * Copyright (c) 2000-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 */

describe('Data suite: Zwoods', function() {
	let submission;

	before(function() {
		const title = 'Finocchiaro: Arguments About Arguments';
		submission = {
			id: 0,
			section: 'Preprints',
			prefix: '',
			title: title,
			subtitle: '',
			abstract: 'None.',
			shortAuthorString: 'Woods',
			authorNames: ['<PERSON><PERSON> Woods'],
			sectionId: 1,
			assignedAuthorNames: ['<PERSON><PERSON> Woods'],
			files: [
				{
					'file': 'dummy.pdf',
					'fileName': title + '.pdf',
					'mimeType': 'application/pdf',
					'genre': Cypress.env('defaultGenre')
				},
			],
			keywords: [
				'education',
				'citizenship'
			]
		};
	});

	it('Create a submission', function() {
		cy.register({
			'username': 'z<PERSON>',
			'givenName': '<PERSON><PERSON>',
			'familyName': '<PERSON>',
			'affiliation': 'CUNY',
			'country': 'United States',
		});

		cy.getCsrfToken();
		cy.window()
			.then(() => {
				return cy.createSubmissionWithApi(submission, this.csrfToken);
			})
			.then(xhr => {
				return cy.submitSubmissionWithApi(submission.id, this.csrfToken);
			});

		cy.logout();
		cy.findSubmissionAsEditor('dbarnes', null, 'Woods');
		cy.get('button:contains("Post the preprint")').click();
		cy.get('button:contains("Post"):visible').click();
		cy.get('div:contains("All requirements have been met. Are you sure you want to post this?")');
		cy.get('[id^="publish"] button:contains("Post")').click();
	});
});
