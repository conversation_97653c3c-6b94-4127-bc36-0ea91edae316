/**
 * @file cypress/tests/data/60-content/KalkhafajiSubmission.cy.js
 *
 * Copyright (c) 2014-2025 Simon <PERSON> University
 * Copyright (c) 2000-2025 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 */

describe('Data suite: Kalkhafaji', function() {
	let submission;

	before(function() {
		const title = 'Learning Sustainable Design through Service';
		submission = {
			id: 0,
			section: 'Preprints',
			prefix: '',
			title: title,
			subtitle: '',
			abstract: 'Environmental sustainability and sustainable development principles are vital topics that engineering education has largely failed to address. Service-learning, which integrates social service into an academic setting, is an emerging tool that can be leveraged to teach sustainable design to future engineers. We present a model of using service-learning to teach sustainable design based on the experiences of the Stanford chapter of Engineers for a Sustainable World. The model involves the identification of projects and partner organizations, a student led, project-based design course, and internships coordinated with partner organizations. The model has been very successful, although limitations and challenges exist. These are discussed along with future directions for expanding the model.',
			shortAuthorString: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
			authorNames: ['<PERSON><PERSON>'],
			sectionId: 1,
			assignedAuthorNames: ['<PERSON><PERSON>'],
			additionalAuthors: [
				{
					givenName: {en: '<PERSON>'},
					familyName: {en: 'Morse'},
					country: 'US',
					affiliations: [
						{
							name: {en: 'Stanford University'}
						}
					],
					email: '<EMAIL>',
					userGroupId: Cypress.env('authorUserGroupId')
				}
			],
			files: [
				{
					'file': 'dummy.pdf',
					'fileName': title + '.pdf',
					'mimeType': 'application/pdf',
					'genre': Cypress.env('defaultGenre')
				},
			],
			keywords: [
				'Development',
				'engineering education',
				'service learning',
				'sustainability',
			]
		};
	});

	it('Create a submission', function() {
		cy.register({
			'username': 'kalkhafaji',
			'givenName': 'Karim',
			'familyName': 'Al-Khafaji',
			'affiliation': 'Stanford University',
			'country': 'United States',
		});

		cy.getCsrfToken();
		cy.window()
			.then(() => {
				return cy.createSubmissionWithApi(submission, this.csrfToken);
			})
			.then(xhr => {
				return cy.submitSubmissionWithApi(submission.id, this.csrfToken);
			});

		cy.logout();
		cy.findSubmissionAsEditor('dbarnes', null, 'Al-Khafaji');
		cy.get('button:contains("Post the preprint")').click();
		cy.get('button:contains("Post"):visible').click();
		cy.get('div:contains("All requirements have been met. Are you sure you want to post this?")');
		cy.get('[id^="publish"] button:contains("Post")').click();
	});
});
