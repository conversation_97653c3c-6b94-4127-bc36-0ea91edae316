#!/bin/bash

# OPS开发环境停止脚本
# 用于停止所有OPS开发服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止OPS开发服务器
stop_ops_server() {
    log_info "停止OPS开发服务器..."
    
    # 查找并停止PHP开发服务器进程
    if pgrep -f "php -S localhost:8000" > /dev/null; then
        pkill -f "php -S localhost:8000"
        log_success "OPS开发服务器已停止"
    else
        log_warning "OPS开发服务器未运行"
    fi
}

# 停止PostgreSQL容器
stop_postgres() {
    log_info "停止PostgreSQL和pgAdmin容器..."
    
    if [[ -f "docker/postgres-setup.sh" ]]; then
        ./docker/postgres-setup.sh stop
        log_success "PostgreSQL容器已停止"
    else
        log_warning "找不到docker/postgres-setup.sh脚本，尝试直接停止容器..."
        docker-compose down || log_warning "无法通过docker-compose停止容器"
    fi
}

# 清理临时文件
cleanup_files() {
    log_info "清理临时文件..."
    
    # 删除服务器日志文件（可选）
    if [[ -f "ops-server.log" ]]; then
        if [[ "$1" == "--clean-logs" ]]; then
            rm -f ops-server.log
            log_success "已删除服务器日志文件"
        else
            log_info "保留服务器日志文件 (使用 --clean-logs 删除)"
        fi
    fi
    
    # 清理其他临时文件
    rm -f nohup.out
}

# 显示停止状态
show_stop_status() {
    echo
    echo "=========================================="
    echo -e "${GREEN}🛑 OPS开发环境已停止${NC}"
    echo "=========================================="
    echo
    
    # 检查服务状态
    echo -e "${BLUE}📊 服务状态：${NC}"
    
    # PostgreSQL
    if docker ps | grep -q "ops_postgres"; then
        echo -e "  • PostgreSQL: ${YELLOW}仍在运行${NC}"
    else
        echo -e "  • PostgreSQL: ${GREEN}已停止${NC}"
    fi
    
    # pgAdmin
    if docker ps | grep -q "ops_pgadmin"; then
        echo -e "  • pgAdmin: ${YELLOW}仍在运行${NC}"
    else
        echo -e "  • pgAdmin: ${GREEN}已停止${NC}"
    fi
    
    # OPS服务器
    if pgrep -f "php -S localhost:8000" > /dev/null; then
        echo -e "  • OPS服务器: ${YELLOW}仍在运行${NC}"
    else
        echo -e "  • OPS服务器: ${GREEN}已停止${NC}"
    fi
    
    echo
    echo -e "${BLUE}🔄 重新启动：${NC}"
    echo "  • 启动所有服务: ./start-ops-dev.sh"
    echo "  • 重启服务: ./restart-ops-dev.sh"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}=========================================="
    echo "OPS开发环境停止"
    echo "==========================================${NC}"
    echo
    
    stop_ops_server
    stop_postgres
    cleanup_files "$@"
    show_stop_status
}

# 运行主函数
main "$@"
