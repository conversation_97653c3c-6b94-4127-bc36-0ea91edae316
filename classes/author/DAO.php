<?php

/**
 * @file classes/author/DAO.php
 *
 * Copyright (c) 2014-2021 Simon <PERSON> University
 * Copyright (c) 2003-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class DAO
 *
 * @ingroup author
 *
 * @see Author
 *
 * @brief Operations for retrieving and modifying Author objects.
 */

namespace APP\author;

class DAO extends \PKP\author\DAO
{
}
