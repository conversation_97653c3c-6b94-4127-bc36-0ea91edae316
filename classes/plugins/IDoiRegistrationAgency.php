<?php

/**
 * @file classes/plugins/IDoiRegistrationAgency.php
 *
 * Copyright (c) 2014-2021 Simon <PERSON> University
 * Copyright (c) 2003-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class IDoiRegistrationAgency
 *
 * @ingroup plugins
 *
 * @brief Interface that registration agency plugins must implement to support DOI registrations.
 */

namespace APP\plugins;

use APP\submission\Submission;
use PKP\context\Context;
use PKP\plugins\IPKPDoiRegistrationAgency;

interface IDoiRegistrationAgency extends IPKPDoiRegistrationAgency
{
    /**
     * @param Submission[] $submissions
     *
     */
    public function exportSubmissions(array $submissions, Context $context): array;

    /**
     * @param Submission[] $submissions
     *
     */
    public function depositSubmissions(array $submissions, Context $context): array;
}
