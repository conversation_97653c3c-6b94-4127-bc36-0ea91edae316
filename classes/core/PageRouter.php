<?php

/**
 * @file classes/core/PageRouter.php
 *
 * Copyright (c) 2014-2021 Simon <PERSON> University
 * Copyright (c) 2003-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class PageRouter
 *
 * @ingroup core
 *
 * @brief Class providing OPS-specific page routing.
 */

namespace APP\core;

class PageRouter extends \PKP\core\PKPPageRouter
{
    /**
     * get the cacheable pages
     */
    public function getCacheablePages(): array
    {
        return ['about', 'announcement', 'help', 'index', 'information', ''];
    }
}

if (!PKP_STRICT_MODE) {
    class_alias('\APP\core\PageRouter', '\PageRouter');
}
