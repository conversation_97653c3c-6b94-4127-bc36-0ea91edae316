<?php

/**
 * @file classes/mail/variables/ContextEmailVariable.php
 *
 * Copyright (c) 2014-2021 Simon <PERSON>
 * Copyright (c) 2000-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class ContextEmailVariable
 *
 * @ingroup mail_variables
 *
 * @brief Represents server-specific email template variables
 */

namespace APP\mail\variables;

use PKP\mail\variables\ContextEmailVariable as PKPContextEmailVariable;

class ContextEmailVariable extends PKPContextEmailVariable
{
    public const CONTEXT_NAME = 'serverName';
    public const CONTEXT_URL = 'serverUrl';
    public const CONTEXT_SIGNATURE = 'serverSignature';

    /**
     * @copydoc Variable::values()
     */
    public function values(string $locale): array
    {
        $values = parent::values($locale);

        // Pass the values into the context signature so variables
        // used in the signature can be rendered.
        $values[static::CONTEXT_SIGNATURE] = $this->getContextSignature($values);

        return $values;
    }
}
