<?php

/**
 * @file classes/statistics/TemporaryItemRequestsDAO.php
 *
 * Copyright (c) 2022 Simon <PERSON> University
 * Copyright (c) 2022 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class TemporaryItemRequestsDAO
 *
 * @ingroup statistics
 *
 * @brief Operations for retrieving and adding unique item (submission) requests (primary files downloads).
 */

namespace APP\statistics;

use PKP\statistics\PKPTemporaryItemRequestsDAO;

class TemporaryItemRequestsDAO extends PKPTemporaryItemRequestsDAO
{
}
