<?php
/**
 * @file classes/components/form/context/ContextForm.php
 *
 * Copyright (c) 2014-2021 Simon <PERSON> University
 * Copyright (c) 2000-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class ContextForm
 *
 * @ingroup classes_controllers_form
 *
 * @brief Add OPS-specific fields to the context add/edit form.
 */

namespace APP\components\forms\context;

use PKP\components\forms\context\PKPContextForm;
use PKP\components\forms\FieldOptions;
use PKP\components\forms\FieldText;

class ContextForm extends PKPContextForm
{
    /**
     * @copydoc PKPContextForm::__construct()
     */
    public function __construct($action, $locales, $baseUrl, $context)
    {
        parent::__construct($action, $locales, $baseUrl, $context);

        $this->addField(new FieldText('abbreviation', [
            'label' => __('manager.setup.serverAbbreviation'),
            'isMultilingual' => true,
            'value' => $context ? $context->getData('abbreviation') : null,
        ]), [FIELD_POSITION_AFTER, 'acronym'])
            ->addField(new FieldOptions('enabled', [
                'label' => __('common.enable'),
                'options' => [
                    ['value' => true, 'label' => __('admin.contexts.enableContextInstructions')],
                ],
                'value' => $context ? (bool) $context->getData('enabled') : false,
            ]));
        ;
    }
}
