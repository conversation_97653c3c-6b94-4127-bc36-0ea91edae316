<?php

/**
 * @file classes/migration/upgrade/v3_4_0/I7264_UpdateEmailTemplates.php
 *
 * Copyright (c) 2014-2021 Simon <PERSON> University
 * Copyright (c) 2000-2021 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class I7264_UpdateEmailTemplates
 *
 * @brief Describe upgrade/downgrade operations for DB table email_templates.
 */

namespace APP\migration\upgrade\v3_4_0;

class I7264_UpdateEmailTemplates extends \PKP\migration\upgrade\v3_4_0\I7264_UpdateEmailTemplates
{
    protected function oldNewVariablesMap(): array
    {
        $oldNewVariablesMap = parent::oldNewVariablesMap();
        array_walk_recursive($oldNewVariablesMap, function (&$newVariable, $oldVariable) {
            if ($newVariable === 'contextName') {
                $newVariable = 'serverName';
            } elseif ($newVariable === 'contextUrl') {
                $newVariable = 'serverUrl';
            } elseif ($newVariable === 'contextSignature') {
                $newVariable = 'serverSignature';
            }
        });

        return $oldNewVariablesMap;
    }
}
