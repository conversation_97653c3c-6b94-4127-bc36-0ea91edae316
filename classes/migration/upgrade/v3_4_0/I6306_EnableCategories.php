<?php

/**
 * @file classes/migration/upgrade/v3_4_0/I6306_EnableCategories.php
 *
 * Copyright (c) 2014-2022 Simon <PERSON> University
 * Copyright (c) 2000-2022 <PERSON>
 * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
 *
 * @class I6306_EnableCategories
 */

namespace APP\migration\upgrade\v3_4_0;

class I6306_EnableCategories extends \PKP\migration\upgrade\v3_4_0\I6306_EnableCategories
{
    protected function getContextTable(): string
    {
        return 'servers';
    }
    protected function getContextSettingsTable(): string
    {
        return 'server_settings';
    }
    protected function getContextIdColumn(): string
    {
        return 'server_id';
    }
}
