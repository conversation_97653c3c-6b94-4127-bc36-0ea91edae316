on:
  push:
    branches:
      - '*'
  pull_request:
    branches:
      ['stable-3_5_0']
name: ops-stable-3_5_0
jobs:
  ops:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - php-version: 8.2
            validate: 'validate'
          - php-version: 8.2
            database: pgsql
            test: 'test'
            upgrade: 'upgrade'
            upgrade_test: 'stable-3_3_0,stable-3_4_0'
          - php-version: 8.2
            database: mariadb
            test: 'test'
            upgrade: 'upgrade'
            upgrade_test: 'stable-3_4_0'
          - php-version: 8.2
            database: mysql
            test: 'test'
            upgrade: 'upgrade'
            upgrade_test: 'stable-3_3_0,stable-3_4_0'
          - php-version: 8.3
            database: mysql
            test: 'test'
          - php-version: 8.3
            database: pgsql
            test: 'test'

    name: ops
    steps:
      - uses: pkp/pkp-github-actions@v1
        with:
          node_version: 20
          dataset_branch: 'stable-3_5_0'
          DEBUG_IN_TMATE: false
