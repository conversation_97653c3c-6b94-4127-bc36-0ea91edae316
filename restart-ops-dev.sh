#!/bin/bash

# OPS开发环境重启脚本
# 用于重启所有OPS开发服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 主函数
main() {
    echo -e "${BLUE}=========================================="
    echo "OPS开发环境重启"
    echo "==========================================${NC}"
    echo
    
    log_info "正在重启OPS开发环境..."
    echo
    
    # 停止所有服务
    log_info "第一步: 停止所有服务"
    ./stop-ops-dev.sh
    
    echo
    log_info "等待服务完全停止..."
    sleep 3
    echo
    
    # 启动所有服务
    log_info "第二步: 启动所有服务"
    ./start-ops-dev.sh
    
    echo
    log_success "OPS开发环境重启完成！"
}

# 检查脚本文件是否存在
if [[ ! -f "stop-ops-dev.sh" ]] || [[ ! -f "start-ops-dev.sh" ]]; then
    log_error "找不到必要的脚本文件 (stop-ops-dev.sh 或 start-ops-dev.sh)"
    exit 1
fi

# 运行主函数
main "$@"
