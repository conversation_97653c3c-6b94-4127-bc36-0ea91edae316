<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYP<PERSON> install SYSTEM "../../lib/pkp/dtd/install.dtd">
<!--
  * dbscripts/xml/install.xml
  *
  * Copyright (c) 2014-2025 Simon <PERSON> University
  * Copyright (c) 2003-2025 <PERSON>
  * Distributed under the GNU GPL v3. For full terms see the file docs/COPYING.
  *
  * Installation descriptor file.
  -->

<install version="*******">
	<code function="checkPhpVersion" />

	<code function="createDirectories"/>

	<migration class="APP\migration\install\ServersMigration" />

	<!-- PKP schema components -->
	<migration class="PKP\migration\install\CommonMigration" />
	<migration class="PKP\migration\install\SessionsMigration" />
	<migration class="PKP\migration\install\GenresMigration" />
	<migration class="PKP\migration\install\ControlledVocabMigration" />
	<migration class="PKP\migration\install\FailedJobsMigration" />
	<migration class="PKP\migration\install\FilesMigration" />
	<migration class="PKP\migration\install\JobsMigration" />
	<migration class="PKP\migration\install\LogMigration" />
	<migration class="PKP\migration\install\NavigationMenusMigration" />
	<migration class="PKP\migration\install\NotesMigration" />
	<migration class="PKP\migration\install\RolesAndUserGroupsMigration" />
	<migration class="APP\migration\install\SubmissionsMigration" />
	<migration class="PKP\migration\install\InvitationsMigration" />
	<migration class="PKP\migration\install\ReviewFormsMigration" />
	<migration class="PKP\migration\install\SubmissionFilesMigration" />
	<migration class="PKP\migration\install\LibraryFilesMigration" />
	<migration class="PKP\migration\install\ReviewsMigration" />
	<migration class="PKP\migration\install\ReviewerSuggestionsMigration" />
	<migration class="PKP\migration\install\TemporaryFilesMigration" />
	<migration class="PKP\migration\install\TombstoneMigration" />
	<migration class="PKP\migration\install\DoiMigration" />

	<migration class="APP\migration\install\OPSMigration" />

	<migration class="PKP\migration\install\MetadataMigration" />
	<migration class="PKP\migration\install\AnnouncementsMigration" />
	<migration class="PKP\migration\install\CategoriesMigration" />
	<migration class="PKP\migration\install\HighlightsMigration" />
	<migration class="PKP\migration\install\InstitutionsMigration" />
	<migration class="APP\migration\install\MetricsMigration" />
	<migration class="PKP\migration\install\AffiliationsMigration" />
	<migration class="PKP\migration\install\RorsMigration" />
	<migration class="PKP\migration\install\ReviewAssignmentSettings" />

	<!-- Other install tasks -->
	<code function="createData"/>
	<code function="createConfig"/>
	<code function="addPluginVersions"/>
	<code function="installDefaultNavigationMenus"/>
	<code function="updateRorRegistryDataset"/>
	<code function="downloadIPGeoDB" />
</install>
