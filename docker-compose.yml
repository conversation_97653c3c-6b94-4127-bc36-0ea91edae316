version: '3.8'

services:
  postgres:
    image: postgres:14-alpine
    container_name: ops_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ops
      POSTGRES_USER: ops
      POSTGRES_PASSWORD: ops_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - ops_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ops -d ops"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 可选：添加 pgAdmin 用于数据库管理
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ops_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - ops_network
    depends_on:
      - postgres

volumes:
  postgres_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  ops_network:
    driver: bridge
