# OPS (Open Preprint Systems) 开发环境安装指南

本文档提供了在Ubuntu 22.04上设置OPS开发环境的完整指南，包括PostgreSQL Docker容器、PHP环境、Composer依赖、Node.js构建工具等所有组件。

## 📋 环境要求

### 系统要求
- **操作系统**: Ubuntu 22.04 LTS
- **PHP版本**: PHP 8.3.x
- **内存**: 至少 4GB RAM
- **存储**: 至少 10GB 可用空间
- **网络**: 稳定的互联网连接

### 前置软件
在开始之前，请确保已安装以下软件：

```bash
# 检查PHP版本
php --version  # 应显示 PHP 8.3.x

# 检查Docker
docker --version
docker-compose --version

# 检查Composer
composer --version
```

如果缺少任何组件，请参考 [前置软件安装](#前置软件安装) 部分。

## 🔧 前置软件安装

### 安装PHP 8.3

```bash
# 添加PHP仓库
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update

# 安装PHP 8.3
sudo apt install -y php8.3 php8.3-cli php8.3-fpm

# 验证安装
php --version
```

### 安装Docker和Docker Compose

```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 将当前用户添加到docker组
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo apt install -y docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 安装Composer

```bash
# 下载Composer安装程序
curl -sS https://getcomposer.org/installer | php

# 移动到全局位置
sudo mv composer.phar /usr/local/bin/composer

# 验证安装
composer --version
```

## 🚀 快速安装

### 方法一：使用自动安装脚本（推荐）

1. **下载并运行安装脚本**：
```bash
# 在OPS项目根目录运行
chmod +x setup-ops-dev.sh
./setup-ops-dev.sh
```

2. **按照提示完成安装**，脚本会自动处理所有步骤。

### 方法二：手动安装

如果您希望了解每个步骤的详细过程，请按照以下手动安装步骤进行。

## 📝 手动安装步骤

### 1. 安装Node.js 18.x

```bash
# 添加NodeSource仓库
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -

# 安装Node.js和NPM
sudo apt install -y nodejs

# 验证安装
node --version  # 应显示 v18.x.x
npm --version   # 应显示 10.x.x
```

### 2. 安装PHP扩展

```bash
# 更新包列表
sudo apt update

# 安装必要的PHP扩展
sudo apt install -y \
    php8.3-pgsql \
    php8.3-mbstring \
    php8.3-xml \
    php8.3-curl \
    php8.3-gd \
    php8.3-zip \
    php8.3-intl \
    php8.3-bcmath \
    php8.3-common

# 验证扩展安装
php -m | grep -E "(pgsql|mbstring|xml|curl|gd|zip|intl|bcmath)"
```

**预期输出**：
```
bcmath
curl
gd
intl
libxml
mbstring
pdo_pgsql
pgsql
xml
xmlreader
xmlwriter
zip
```

### 3. 配置OPS项目

```bash
# 确保在OPS项目根目录
cd /path/to/your/ops/project

# 复制配置文件
cp config.inc.php.example config.inc.php

# 修改数据库配置（将host从postgres改为localhost）
sed -i 's/host = postgres/host = localhost/' config.inc.php
```

**验证配置**：
```bash
# 检查数据库配置
grep -A 10 "\[database\]" config.inc.php
```

**预期输出**：
```ini
[database]

; PostgreSQL 驱动
driver = postgres

; 数据库主机（通过端口映射连接）
host = localhost

; 数据库端口
port = 5432

; 数据库用户名
username = ops

; 数据库密码
password = ops_password

; 数据库名称
name = ops
```

### 4. 安装Composer依赖

```bash
# 安装pkp-lib依赖（选择yes继续）
echo "yes" | composer --working-dir=lib/pkp install --no-dev

# 安装citationStyleLanguage插件依赖
echo "yes" | composer --working-dir=plugins/generic/citationStyleLanguage install --no-dev
```

**验证安装**：
```bash
# 检查vendor目录
ls -la lib/pkp/vendor/
ls -la plugins/generic/citationStyleLanguage/vendor/
```

### 5. 安装NPM依赖并构建前端

```bash
# 安装NPM依赖
npm install

# 构建前端资源
npm run build
```

**验证构建**：
```bash
# 检查构建输出
ls -la js/build.js
ls -la js/build_frontend.js
```

### 6. 启动PostgreSQL容器

```bash
# 启动PostgreSQL和pgAdmin容器
./docker/postgres-setup.sh start
```

**验证容器状态**：
```bash
# 检查容器运行状态
docker ps | grep -E "(postgres|pgadmin)"
```

**预期输出**：
```
CONTAINER ID   IMAGE              COMMAND                  CREATED         STATUS         PORTS                    NAMES
xxxxx          postgres:14-alpine "docker-entrypoint.s…"   x minutes ago   Up x minutes   0.0.0.0:5432->5432/tcp   ops_postgres
xxxxx          dpage/pgadmin4     "/entrypoint.sh"         x minutes ago   Up x minutes   0.0.0.0:8080->80/tcp     ops_pgadmin
```

### 7. 启动OPS开发服务器

```bash
# 启动PHP内置开发服务器
php -S localhost:8000
```

**验证服务器**：
```bash
# 在另一个终端测试连接
curl -I http://localhost:8000
```

## 🌐 访问地址

安装完成后，您可以通过以下地址访问各个服务：

| 服务 | 地址 | 用途 |
|------|------|------|
| **OPS主站** | http://localhost:8000 | OPS应用程序主界面 |
| **pgAdmin** | http://localhost:8080 | PostgreSQL数据库管理 |

## 🔑 登录信息

### 数据库连接信息
- **主机**: localhost
- **端口**: 5432
- **数据库**: ops
- **用户名**: ops
- **密码**: ops_password

### pgAdmin登录信息
- **邮箱**: <EMAIL>
- **密码**: admin

## 🔧 开发工作流

### 日常开发命令

```bash
# 启动开发环境
./docker/postgres-setup.sh start
php -S localhost:8000

# 重新构建前端资源
npm run build

# 安装新的Composer包
composer require package/name

# 安装新的NPM包
npm install package-name

# 查看数据库日志
./docker/postgres-setup.sh logs

# 备份数据库
./docker/postgres-setup.sh backup

# 停止所有服务
./docker/postgres-setup.sh stop
pkill -f "php -S localhost:8000"
```

### 代码修改后的操作

1. **PHP代码修改**：无需重启，刷新浏览器即可
2. **前端代码修改**：运行 `npm run build` 重新构建
3. **配置文件修改**：重启PHP服务器
4. **数据库结构修改**：通过OPS管理界面或pgAdmin执行

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. PHP扩展缺失
**错误**: `PHP Fatal error: Uncaught Error: Call to undefined function pg_connect()`

**解决方案**:
```bash
# 重新安装PostgreSQL扩展
sudo apt install php8.3-pgsql
sudo systemctl restart apache2  # 如果使用Apache
```

#### 2. Node.js版本不兼容
**错误**: `npm ERR! engine Unsupported engine`

**解决方案**:
```bash
# 卸载旧版本Node.js
sudo apt remove nodejs npm

# 重新安装Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs
```

#### 3. Docker容器启动失败
**错误**: `Cannot start service postgres: port is already allocated`

**解决方案**:
```bash
# 检查端口占用
sudo netstat -tlnp | grep :5432

# 停止占用端口的服务或修改docker-compose.yml中的端口映射
```

#### 4. Composer权限警告
**错误**: `Do not run Composer as root/super user!`

**解决方案**:
```bash
# 使用普通用户运行，或在命令前添加确认
echo "yes" | composer install
```

#### 5. 前端构建失败
**错误**: `npm ERR! Build failed`

**解决方案**:
```bash
# 清理缓存并重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
npm run build
```

#### 6. 数据库连接失败
**错误**: `SQLSTATE[08006] [7] could not connect to server`

**解决方案**:
```bash
# 检查PostgreSQL容器状态
docker ps | grep postgres

# 重启PostgreSQL容器
./docker/postgres-setup.sh restart

# 检查配置文件中的数据库设置
grep -A 10 "\[database\]" config.inc.php
```

### 日志文件位置

- **OPS服务器日志**: `ops-server.log`（如果使用后台运行）
- **PostgreSQL日志**: `docker logs ops_postgres`
- **pgAdmin日志**: `docker logs ops_pgadmin`
- **NPM构建日志**: 控制台输出

### 性能优化建议

1. **开发环境**:
   - 使用 `npm run dev` 进行开发时的热重载
   - 启用PHP的OPcache扩展
   - 增加PHP内存限制：`memory_limit = 512M`

2. **数据库优化**:
   - 定期清理开发数据
   - 使用数据库索引优化查询
   - 监控数据库连接数

## 📚 相关文档

- [PKP开发文档](https://docs.pkp.sfu.ca/dev/)
- [OPS用户指南](https://docs.pkp.sfu.ca/ops/)
- [PostgreSQL文档](https://www.postgresql.org/docs/)
- [Docker Compose文档](https://docs.docker.com/compose/)

## 🤝 贡献

如果您在使用过程中发现问题或有改进建议，请：

1. 检查现有的故障排除部分
2. 查看相关日志文件
3. 提交详细的问题报告

---

**最后更新**: 2025-07-27  
**适用版本**: OPS 3.5.0, Ubuntu 22.04, PHP 8.3
