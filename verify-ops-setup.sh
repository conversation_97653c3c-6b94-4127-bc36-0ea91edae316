#!/bin/bash

# OPS开发环境验证脚本
# 检查所有组件是否正确安装和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_command() {
    local cmd="$1"
    local name="$2"
    local expected="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查 $name... "
    
    if command -v "$cmd" &> /dev/null; then
        local version
        if [[ "$cmd" == "composer" ]]; then
            version=$(echo "yes" | $cmd --version --no-interaction 2>/dev/null | head -1 || echo "已安装")
        else
            version=$($cmd --version 2>/dev/null | head -1 || echo "已安装")
        fi

        if [[ -n "$expected" ]]; then
            if echo "$version" | grep -q "$expected"; then
                echo -e "${GREEN}✓${NC} $version"
                PASSED_CHECKS=$((PASSED_CHECKS + 1))
            else
                echo -e "${RED}✗${NC} 版本不匹配: $version (期望: $expected)"
                FAILED_CHECKS=$((FAILED_CHECKS + 1))
            fi
        else
            echo -e "${GREEN}✓${NC} $version"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        fi
    else
        echo -e "${RED}✗${NC} 未安装"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 检查文件存在
check_file() {
    local file="$1"
    local name="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查 $name... "
    
    if [[ -f "$file" ]]; then
        echo -e "${GREEN}✓${NC} 存在"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} 不存在: $file"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 检查目录存在
check_directory() {
    local dir="$1"
    local name="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查 $name... "
    
    if [[ -d "$dir" ]]; then
        echo -e "${GREEN}✓${NC} 存在"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} 不存在: $dir"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 检查PHP扩展
check_php_extension() {
    local ext="$1"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查PHP扩展 $ext... "
    
    if php -m | grep -q "^$ext$"; then
        echo -e "${GREEN}✓${NC} 已安装"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} 未安装"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 检查服务状态
check_service() {
    local url="$1"
    local name="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查 $name 服务... "
    
    if curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo -e "${GREEN}✓${NC} 运行中"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} 无法访问"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 检查Docker容器
check_docker_container() {
    local container="$1"
    local name="$2"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查Docker容器 $name... "
    
    if docker ps | grep -q "$container"; then
        echo -e "${GREEN}✓${NC} 运行中"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} 未运行"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 主验证函数
main() {
    echo -e "${BLUE}=========================================="
    echo "OPS开发环境验证脚本"
    echo "==========================================${NC}"
    echo
    
    # 检查系统信息
    echo -e "${BLUE}📋 系统信息${NC}"
    echo "操作系统: $(lsb_release -d | cut -f2)"
    echo "内核版本: $(uname -r)"
    echo "当前用户: $(whoami)"
    echo "工作目录: $(pwd)"
    echo
    
    # 检查基础命令
    echo -e "${BLUE}🔧 基础软件${NC}"
    check_command "php" "PHP" "8.3"
    check_command "composer" "Composer"
    check_command "node" "Node.js" "v18"
    check_command "npm" "NPM"
    check_command "docker" "Docker"
    check_command "docker-compose" "Docker Compose"
    echo
    
    # 检查PHP扩展
    echo -e "${BLUE}🧩 PHP扩展${NC}"
    check_php_extension "pgsql"
    check_php_extension "pdo_pgsql"
    check_php_extension "mbstring"
    check_php_extension "xml"
    check_php_extension "curl"
    check_php_extension "gd"
    check_php_extension "zip"
    check_php_extension "intl"
    check_php_extension "bcmath"
    echo
    
    # 检查项目文件
    echo -e "${BLUE}📁 项目文件${NC}"
    check_file "config.inc.php" "OPS配置文件"
    check_file "docker-compose.yml" "Docker Compose配置"
    check_file "package.json" "NPM配置文件"
    check_file "docker/postgres-setup.sh" "PostgreSQL管理脚本"
    echo
    
    # 检查依赖目录
    echo -e "${BLUE}📦 依赖包${NC}"
    check_directory "lib/pkp/lib/vendor" "PKP Composer依赖"
    check_directory "plugins/generic/citationStyleLanguage/vendor" "CSL插件依赖"
    check_directory "node_modules" "NPM依赖"
    echo
    
    # 检查构建文件
    echo -e "${BLUE}🏗️ 构建文件${NC}"
    check_file "js/build.js" "主要构建文件"
    check_file "js/build_frontend.js" "前端构建文件"
    echo
    
    # 检查Docker容器
    echo -e "${BLUE}🐳 Docker容器${NC}"
    check_docker_container "ops_postgres" "PostgreSQL"
    check_docker_container "ops_pgadmin" "pgAdmin"
    echo
    
    # 检查服务
    echo -e "${BLUE}🌐 服务状态${NC}"
    check_service "http://localhost:8000" "OPS开发服务器"
    check_service "http://localhost:8080" "pgAdmin"

    # 特殊检查PostgreSQL端口
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "检查 PostgreSQL 数据库... "
    if nc -z localhost 5432 2>/dev/null; then
        echo -e "${GREEN}✓${NC} 端口5432可访问"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${RED}✗${NC} 端口5432不可访问"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
    echo
    
    # 显示结果摘要
    echo -e "${BLUE}=========================================="
    echo "验证结果摘要"
    echo "==========================================${NC}"
    echo -e "总检查项: $TOTAL_CHECKS"
    echo -e "${GREEN}通过: $PASSED_CHECKS${NC}"
    echo -e "${RED}失败: $FAILED_CHECKS${NC}"
    echo
    
    if [[ $FAILED_CHECKS -eq 0 ]]; then
        echo -e "${GREEN}🎉 所有检查都通过了！OPS开发环境已正确设置。${NC}"
        echo
        echo -e "${BLUE}🚀 下一步操作：${NC}"
        echo "1. 访问 http://localhost:8000 开始使用OPS"
        echo "2. 访问 http://localhost:8080 管理数据库"
        echo "3. 开始您的开发工作！"
        exit 0
    else
        echo -e "${RED}❌ 发现 $FAILED_CHECKS 个问题，请检查上述失败项。${NC}"
        echo
        echo -e "${YELLOW}💡 常见解决方案：${NC}"
        echo "• 运行安装脚本: ./setup-ops-dev.sh"
        echo "• 启动服务: ./docker/postgres-setup.sh start && php -S localhost:8000"
        echo "• 重新构建: npm run build"
        echo "• 查看文档: cat OPS_DEVELOPMENT_SETUP.md"
        exit 1
    fi
}

# 运行主函数
main "$@"
