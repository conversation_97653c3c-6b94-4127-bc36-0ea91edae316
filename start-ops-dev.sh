#!/bin/bash

# OPS开发环境快速启动脚本
# 用于在已安装的环境中快速启动所有服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在OPS项目目录
check_project_directory() {
    if [[ ! -f "config.inc.php" ]] || [[ ! -f "docker-compose.yml" ]]; then
        log_error "请在OPS项目根目录运行此脚本"
        exit 1
    fi
}

# 检查Docker是否运行
check_docker() {
    if ! docker info &> /dev/null; then
        log_error "Docker未运行，请启动Docker服务"
        exit 1
    fi
}

# 启动PostgreSQL容器
start_postgres() {
    log_info "启动PostgreSQL和pgAdmin容器..."
    
    if [[ ! -f "docker/postgres-setup.sh" ]]; then
        log_error "找不到docker/postgres-setup.sh脚本"
        exit 1
    fi
    
    ./docker/postgres-setup.sh start
    log_success "PostgreSQL容器启动完成"
}

# 启动OPS开发服务器
start_ops_server() {
    log_info "启动OPS开发服务器..."
    
    # 检查端口是否被占用
    if netstat -tlnp 2>/dev/null | grep -q ":8000 "; then
        log_warning "端口8000已被占用，尝试停止现有服务..."
        pkill -f "php -S localhost:8000" || true
        sleep 2
    fi
    
    # 启动服务器
    log_info "在后台启动PHP开发服务器..."
    nohup php -S localhost:8000 > ops-server.log 2>&1 &
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否启动成功
    if curl -s http://localhost:8000 > /dev/null; then
        log_success "OPS开发服务器启动完成 (PID: $!)"
    else
        log_error "OPS开发服务器启动失败，请检查 ops-server.log"
        exit 1
    fi
}

# 显示状态信息
show_status() {
    echo
    echo "=========================================="
    echo -e "${GREEN}🚀 OPS开发环境已启动！${NC}"
    echo "=========================================="
    echo
    
    # 检查服务状态
    echo -e "${BLUE}📊 服务状态：${NC}"
    
    # PostgreSQL
    if docker ps | grep -q "ops_postgres"; then
        echo -e "  • PostgreSQL: ${GREEN}运行中${NC}"
    else
        echo -e "  • PostgreSQL: ${RED}未运行${NC}"
    fi
    
    # pgAdmin
    if docker ps | grep -q "ops_pgadmin"; then
        echo -e "  • pgAdmin: ${GREEN}运行中${NC}"
    else
        echo -e "  • pgAdmin: ${RED}未运行${NC}"
    fi
    
    # OPS服务器
    if curl -s http://localhost:8000 > /dev/null; then
        echo -e "  • OPS服务器: ${GREEN}运行中${NC}"
    else
        echo -e "  • OPS服务器: ${RED}未运行${NC}"
    fi
    
    echo
    echo -e "${BLUE}🌐 访问地址：${NC}"
    echo "  • OPS主站: http://localhost:8000"
    echo "  • pgAdmin: http://localhost:8080"
    echo
    echo -e "${BLUE}🔑 登录信息：${NC}"
    echo "  • 数据库: ops/ops_password@localhost:5432/ops"
    echo "  • pgAdmin: <EMAIL>/admin"
    echo
    echo -e "${BLUE}🛠️ 管理命令：${NC}"
    echo "  • 停止所有服务: ./stop-ops-dev.sh"
    echo "  • 重启服务: ./restart-ops-dev.sh"
    echo "  • 查看状态: ./verify-ops-setup.sh"
    echo "  • 查看日志: tail -f ops-server.log"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}=========================================="
    echo "OPS开发环境快速启动"
    echo "==========================================${NC}"
    echo
    
    check_project_directory
    check_docker
    start_postgres
    start_ops_server
    show_status
}

# 错误处理
trap 'log_error "启动过程中发生错误"; exit 1' ERR

# 运行主函数
main "$@"
