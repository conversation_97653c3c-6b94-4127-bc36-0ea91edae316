<?php

/**
 * Open Preprint Systems Configuration File - PostgreSQL Example
 * 
 * 这是一个针对 PostgreSQL 14 Docker 容器的示例配置文件
 * 复制此文件为 config.inc.php 并根据需要修改设置
 */

;;;;;;;;;;;;;;;;;;
; General Settings ;
;;;;;;;;;;;;;;;;;;

[general]

; 安装的基础 URL
base_url = "http://localhost"

; 会话 cookie 名称
session_cookie_name = ops_session

; 会话 cookie 路径
session_cookie_path = "/"

; 禁用路径信息（如果遇到 URL 问题）
disable_path_info = Off

; 启用 REST API
restful_urls = On

; 时区设置
default_timezone = "UTC"

;;;;;;;;;;;;;;;;;;;;;
; Database Settings ;
;;;;;;;;;;;;;;;;;;;;;

[database]

; PostgreSQL 驱动
driver = postgres

; 数据库主机（Docker 容器名称）
host = postgres

; 数据库端口
port = 5432

; 数据库用户名
username = ops

; 数据库密码
password = ops_password

; 数据库名称
name = ops

; 数据库连接字符集
connection_charset = utf8

; 启用数据库调试输出（开发时使用）
debug = Off

; 持久连接
persistent = Off

;;;;;;;;;;;;;;;;;;
; Cache Settings ;
;;;;;;;;;;;;;;;;;;

[cache]

; 选择缓存实现 (可选: file, memcache, redis, xcache, apc)
cache = file

; 缓存目录（仅用于文件缓存）
cache_dir = cache

; Memcache 设置（如果使用 memcache）
; memcache_hostname = localhost
; memcache_port = 11211

;;;;;;;;;;;;;;;;;;;
; Security Settings ;
;;;;;;;;;;;;;;;;;;;

[security]

; 强制 SSL 连接
force_ssl = Off

; 仅登录时强制 SSL
force_login_ssl = Off

; 会话检查 IP
session_check_ip = On

; 加密密钥（请更改为随机字符串）
encryption_key = "your-secret-encryption-key-here"

; API 密钥加密
api_key_secret = "your-api-secret-key-here"

; 哈希算法
hash_algorithm = sha256

;;;;;;;;;;;;;;;;;;;
; Email Settings ;
;;;;;;;;;;;;;;;;;;;

[email]

; 默认发件人邮箱
default_envelope_sender = "noreply@localhost"

; SMTP 设置
smtp = Off
; smtp_server = localhost
; smtp_port = 25
; smtp_auth = Off
; smtp_username = ""
; smtp_password = ""

;;;;;;;;;;;;;;;;;;;
; Files Settings ;
;;;;;;;;;;;;;;;;;;;

[files]

; 文件目录
files_dir = files

; 公共文件目录
public_files_dir = public

; 最大文件上传大小（字节）
max_file_size = 10485760

; 允许的文件类型
allowed_file_types = "gif jpg jpeg png pdf doc docx xls xlsx ppt pptx ps eps zip tar gz"

;;;;;;;;;;;;;;;;;;;;
; Internationalization ;
;;;;;;;;;;;;;;;;;;;;

[i18n]

; 默认语言
locale = en

; 数据库连接字符集
connection_charset = utf8

;;;;;;;;;;;;;;;;;;;
; Search Settings ;
;;;;;;;;;;;;;;;;;;;

[search]

; 搜索索引
index = database

; 最小搜索词长度
min_word_length = 3

;;;;;;;;;;;;;;;;;;;
; OAI Settings ;
;;;;;;;;;;;;;;;;;;;

[oai]

; OAI 仓库标识符
repository_id = "ops.localhost"

;;;;;;;;;;;;;;;;;;;;
; Debug Settings ;
;;;;;;;;;;;;;;;;;;;;

[debug]

; 显示错误
display_errors = Off

; 日志错误
log_errors = On

; 错误日志文件
error_log = ""

; 调试模式
debug = Off

; 显示统计信息
show_stats = Off
