#!/bin/bash

# OPS (Open Preprint Systems) 开发环境自动安装脚本
# 适用于 Ubuntu 22.04 + PHP 8.3
# 作者: Augment Agent
# 日期: 2025-07-27

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warning "检测到以root用户运行，建议使用sudo权限的普通用户"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查系统要求
check_system() {
    log_info "检查系统要求..."
    
    # 检查Ubuntu版本
    if ! grep -q "Ubuntu 22.04" /etc/os-release; then
        log_error "此脚本仅支持Ubuntu 22.04"
        exit 1
    fi
    
    # 检查PHP版本
    if ! php --version | grep -q "PHP 8.3"; then
        log_error "需要PHP 8.3，请先安装PHP 8.3"
        exit 1
    fi
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "需要Docker，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "需要Docker Compose，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 安装Node.js
install_nodejs() {
    log_info "安装Node.js 18.x..."
    
    # 添加NodeSource仓库
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    
    # 安装Node.js
    sudo apt install -y nodejs
    
    # 验证安装
    node_version=$(node --version)
    npm_version=$(npm --version)
    log_success "Node.js安装完成: $node_version, NPM: $npm_version"
}

# 安装PHP扩展
install_php_extensions() {
    log_info "安装PHP 8.3扩展..."
    
    sudo apt update
    sudo apt install -y \
        php8.3-pgsql \
        php8.3-mbstring \
        php8.3-xml \
        php8.3-curl \
        php8.3-gd \
        php8.3-zip \
        php8.3-intl \
        php8.3-bcmath \
        php8.3-common
    
    # 验证扩展
    log_info "验证PHP扩展..."
    php -m | grep -E "(pgsql|mbstring|xml|curl|gd|zip|intl|bcmath)" || {
        log_error "PHP扩展安装失败"
        exit 1
    }
    
    log_success "PHP扩展安装完成"
}

# 验证Composer
verify_composer() {
    log_info "验证Composer..."
    
    if ! command -v composer &> /dev/null; then
        log_error "Composer未安装，请先安装Composer"
        exit 1
    fi
    
    composer_version=$(composer --version --no-interaction 2>/dev/null | head -1 || echo "yes" | composer --version)
    log_success "Composer已安装: $composer_version"
}

# 配置OPS项目
configure_ops() {
    log_info "配置OPS项目..."
    
    # 检查是否在OPS项目目录
    if [[ ! -f "config.inc.php.example" ]]; then
        log_error "请在OPS项目根目录运行此脚本"
        exit 1
    fi
    
    # 复制配置文件
    cp config.inc.php.example config.inc.php
    
    # 修改数据库配置
    log_info "配置数据库连接..."
    sed -i 's/host = postgres/host = localhost/' config.inc.php
    
    log_success "OPS配置完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装Composer依赖..."
    
    # 安装pkp-lib依赖
    echo "yes" | composer --working-dir=lib/pkp install --no-dev
    
    # 安装citationStyleLanguage插件依赖
    echo "yes" | composer --working-dir=plugins/generic/citationStyleLanguage install --no-dev
    
    log_success "Composer依赖安装完成"
    
    log_info "安装NPM依赖..."
    npm install
    
    log_success "NPM依赖安装完成"
}

# 构建前端资源
build_frontend() {
    log_info "构建前端资源..."
    
    npm run build
    
    log_success "前端资源构建完成"
}

# 启动服务
start_services() {
    log_info "启动PostgreSQL容器..."
    
    # 检查Docker脚本
    if [[ ! -f "docker/postgres-setup.sh" ]]; then
        log_error "找不到docker/postgres-setup.sh脚本"
        exit 1
    fi
    
    # 启动PostgreSQL
    ./docker/postgres-setup.sh start
    
    log_success "PostgreSQL容器启动完成"
    
    log_info "启动OPS开发服务器..."
    log_info "服务器将在后台运行，使用 'pkill -f \"php -S localhost:8000\"' 停止"
    
    # 启动PHP开发服务器（后台运行）
    nohup php -S localhost:8000 > ops-server.log 2>&1 &
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否启动
    if curl -s http://localhost:8000 > /dev/null; then
        log_success "OPS开发服务器启动完成"
    else
        log_error "OPS开发服务器启动失败，请检查 ops-server.log"
        exit 1
    fi
}

# 显示访问信息
show_access_info() {
    echo
    echo "=========================================="
    echo -e "${GREEN}🎉 OPS开发环境安装完成！${NC}"
    echo "=========================================="
    echo
    echo -e "${BLUE}📋 访问地址：${NC}"
    echo "  • OPS主站: http://localhost:8000"
    echo "  • pgAdmin: http://localhost:8080"
    echo
    echo -e "${BLUE}🔑 数据库连接信息：${NC}"
    echo "  • 主机: localhost"
    echo "  • 端口: 5432"
    echo "  • 数据库: ops"
    echo "  • 用户名: ops"
    echo "  • 密码: ops_password"
    echo
    echo -e "${BLUE}🔧 pgAdmin登录信息：${NC}"
    echo "  • 邮箱: <EMAIL>"
    echo "  • 密码: admin"
    echo
    echo -e "${BLUE}🚀 下一步操作：${NC}"
    echo "  1. 访问 http://localhost:8000 完成OPS安装向导"
    echo "  2. 使用上述数据库连接信息配置数据库"
    echo "  3. 开始开发工作！"
    echo
    echo -e "${YELLOW}💡 管理命令：${NC}"
    echo "  • 停止OPS服务器: pkill -f \"php -S localhost:8000\""
    echo "  • 重启PostgreSQL: ./docker/postgres-setup.sh restart"
    echo "  • 查看服务器日志: tail -f ops-server.log"
    echo "  • 重新构建前端: npm run build"
    echo
}

# 主函数
main() {
    echo -e "${BLUE}=========================================="
    echo "OPS开发环境自动安装脚本"
    echo "适用于Ubuntu 22.04 + PHP 8.3"
    echo "==========================================${NC}"
    echo
    
    check_root
    check_system
    verify_composer
    install_nodejs
    install_php_extensions
    configure_ops
    install_dependencies
    build_frontend
    start_services
    show_access_info
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查上述输出"; exit 1' ERR

# 运行主函数
main "$@"
