/*
 jQuery Form Plugin
 version: 3.51.0-2014.06.20
 Requires jQuery v1.5 or later
 Copyright (c) 2014 M. Alsup
 Examples and documentation at: http://malsup.com/jquery/form/
 Project repository: https://github.com/malsup/form
 Dual licensed under the MIT and GPL licenses.
 https://github.com/malsup/form#copyright-and-license
*/
(function(a){"function"===typeof define&&define.amd?define(["jquery"],a):a("undefined"!=typeof jQuery?jQuery:window.Zepto)})(function(a){function b(g){var n=g.data;g.isDefaultPrevented()||(g.preventDefault(),a(g.target).ajaxSubmit(n))}function c(g){var n=g.target,v=a(n);if(!v.is("[type=submit],[type=image]")){n=v.closest("[type=submit]");if(0===n.length)return;n=n[0]}var t=this;t.clk=n;"image"==n.type&&(void 0!==g.offsetX?(t.clk_x=g.offsetX,t.clk_y=g.offsetY):"function"==typeof a.fn.offset?(v=v.offset(),
t.clk_x=g.pageX-v.left,t.clk_y=g.pageY-v.top):(t.clk_x=g.pageX-n.offsetLeft,t.clk_y=g.pageY-n.offsetTop));setTimeout(function(){t.clk=t.clk_x=t.clk_y=null},100)}function d(){if(a.fn.ajaxSubmit.debug){var g="[jquery.form] "+Array.prototype.join.call(arguments,"");window.console&&window.console.log?window.console.log(g):window.opera&&window.opera.postError&&window.opera.postError(g)}}var e=void 0!==a("<input type='file'/>").get(0).files;var f=void 0!==window.FormData;var k=!!a.fn.prop;a.fn.attr2=function(){if(!k)return this.attr.apply(this,
arguments);var g=this.prop.apply(this,arguments);return g&&g.jquery||"string"===typeof g?g:this.attr.apply(this,arguments)};a.fn.ajaxSubmit=function(g){function n(l){l=a.param(l,g.traditional).split("&");var p=l.length,r=[],w;for(w=0;w<p;w++){l[w]=l[w].replace(/\+/g," ");var x=l[w].split("=");r.push([decodeURIComponent(x[0]),decodeURIComponent(x[1])])}return r}function v(l){for(var p=new FormData,r=0;r<l.length;r++)p.append(l[r].name,l[r].value);if(g.extraData)for(l=n(g.extraData),r=0;r<l.length;r++)l[r]&&
p.append(l[r][0],l[r][1]);g.data=null;r=a.extend(!0,{},a.ajaxSettings,g,{contentType:!1,processData:!1,cache:!1,type:A||"POST"});g.uploadProgress&&(r.xhr=function(){var x=a.ajaxSettings.xhr();x.upload&&x.upload.addEventListener("progress",function(y){var H=0,L=y.loaded||y.position,M=y.total;y.lengthComputable&&(H=Math.ceil(L/M*100));g.uploadProgress(y,L,M,H)},!1);return x});r.data=null;var w=r.beforeSend;r.beforeSend=function(x,y){y.data=g.formData?g.formData:p;w&&w.call(this,x,y)};return a.ajax(r)}
function t(l){function p(C){var D=null;try{C.contentWindow&&(D=C.contentWindow.document)}catch(J){d("cannot get iframe.contentWindow document: "+J)}if(D)return D;try{D=C.contentDocument?C.contentDocument:C.document}catch(J){d("cannot get iframe.contentDocument: "+J),D=C.document}return D}function r(){function C(){try{var S=p(P).readyState;d("state = "+S);S&&"uninitialized"==S.toLowerCase()&&setTimeout(C,50)}catch(V){d("Server abort: ",V," (",V.name,")"),w(2),M&&clearTimeout(M),M=void 0}}var D=q.attr2("target"),
J=q.attr2("action"),N=q.attr("enctype")||q.attr("encoding")||"multipart/form-data";x.setAttribute("target",Y);A&&!/post/i.test(A)||x.setAttribute("method","POST");J!=u.url&&x.setAttribute("action",u.url);u.skipEncodingOverride||A&&!/post/i.test(A)||q.attr({encoding:"multipart/form-data",enctype:"multipart/form-data"});u.timeout&&(M=setTimeout(function(){L=!0;w(1)},u.timeout));var Q=[];try{if(u.extraData)for(var O in u.extraData)u.extraData.hasOwnProperty(O)&&(a.isPlainObject(u.extraData[O])&&u.extraData[O].hasOwnProperty("name")&&
u.extraData[O].hasOwnProperty("value")?Q.push(a('<input type="hidden" name="'+u.extraData[O].name+'">').val(u.extraData[O].value).appendTo(x)[0]):Q.push(a('<input type="hidden" name="'+O+'">').val(u.extraData[O]).appendTo(x)[0]));u.iframeTarget||T.appendTo("body");P.attachEvent?P.attachEvent("onload",w):P.addEventListener("load",w,!1);setTimeout(C,15);try{x.submit()}catch(S){document.createElement("form").submit.apply(x)}}finally{x.setAttribute("action",J),x.setAttribute("enctype",N),D?x.setAttribute("target",
D):q.removeAttr("target"),a(Q).remove()}}function w(C){if(!z.aborted&&!ba)if(I=p(P),I||(d("cannot access response document"),C=2),1===C&&z)z.abort("timeout"),R.reject(z,"timeout");else if(2==C&&z)z.abort("server abort"),R.reject(z,"error","server abort");else if(I&&I.location.href!=u.iframeSrc||L){P.detachEvent?P.detachEvent("onload",w):P.removeEventListener("load",w,!1);C="success";var D;try{if(L)throw"timeout";var J="xml"==u.dataType||I.XMLDocument||a.isXMLDoc(I);d("isXml="+J);if(!J&&window.opera&&
(null===I.body||!I.body.innerHTML)&&--da){d("requeing onLoad callback, DOM not available");setTimeout(w,250);return}var N=I.body?I.body:I.documentElement;z.responseText=N?N.innerHTML:null;z.responseXML=I.XMLDocument?I.XMLDocument:I;J&&(u.dataType="xml");z.getResponseHeader=function(W){return{"content-type":u.dataType}[W.toLowerCase()]};N&&(z.status=Number(N.getAttribute("status"))||z.status,z.statusText=N.getAttribute("statusText")||z.statusText);var Q=(u.dataType||"").toLowerCase(),O=/(json|script|text)/.test(Q);
if(O||u.textarea){var S=I.getElementsByTagName("textarea")[0];if(S)z.responseText=S.value,z.status=Number(S.getAttribute("status"))||z.status,z.statusText=S.getAttribute("statusText")||z.statusText;else if(O){var V=I.getElementsByTagName("pre")[0],Z=I.getElementsByTagName("body")[0];V?z.responseText=V.textContent?V.textContent:V.innerText:Z&&(z.responseText=Z.textContent?Z.textContent:Z.innerText)}}else"xml"==Q&&!z.responseXML&&z.responseText&&(z.responseXML=ea(z.responseText));try{ca=fa(z,Q,u)}catch(W){C=
"parsererror",z.error=D=W||C}}catch(W){d("error caught: ",W),C="error",z.error=D=W||C}z.aborted&&(d("upload aborted"),C=null);z.status&&(C=200<=z.status&&300>z.status||304===z.status?"success":"error");"success"===C?(u.success&&u.success.call(u.context,ca,"success",z),R.resolve(z.responseText,"success",z),H&&a.event.trigger("ajaxSuccess",[z,u])):C&&(void 0===D&&(D=z.statusText),u.error&&u.error.call(u.context,z,C,D),R.reject(z,"error",D),H&&a.event.trigger("ajaxError",[z,u,D]));H&&a.event.trigger("ajaxComplete",
[z,u]);H&&!--a.active&&a.event.trigger("ajaxStop");u.complete&&u.complete.call(u.context,z,C);ba=!0;u.timeout&&clearTimeout(M);setTimeout(function(){u.iframeTarget?T.attr("src",u.iframeSrc):T.remove();z.responseXML=null},100)}}var x=q[0],y,H,L,M,R=a.Deferred();R.abort=function(C){z.abort(C)};if(l)for(y=0;y<K.length;y++)l=a(K[y]),k?l.prop("disabled",!1):l.removeAttr("disabled");var u=a.extend(!0,{},a.ajaxSettings,g);u.context=u.context||u;var Y="jqFormIO"+(new Date).getTime();if(u.iframeTarget){var T=
a(u.iframeTarget);(y=T.attr2("name"))?Y=y:T.attr2("name",Y)}else T=a('<iframe name="'+Y+'" src="'+u.iframeSrc+'" />'),T.css({position:"absolute",top:"-1000px",left:"-1000px"});var P=T[0];var z={aborted:0,responseText:null,responseXML:null,status:0,statusText:"n/a",getAllResponseHeaders:function(){},getResponseHeader:function(){},setRequestHeader:function(){},abort:function(C){var D="timeout"===C?"timeout":"aborted";d("aborting upload... "+D);this.aborted=1;try{P.contentWindow.document.execCommand&&
P.contentWindow.document.execCommand("Stop")}catch(J){}T.attr("src",u.iframeSrc);z.error=D;u.error&&u.error.call(u.context,z,D,C);H&&a.event.trigger("ajaxError",[z,u,D]);u.complete&&u.complete.call(u.context,z,D)}};(H=u.global)&&0===a.active++&&a.event.trigger("ajaxStart");H&&a.event.trigger("ajaxSend",[z,u]);if(u.beforeSend&&!1===u.beforeSend.call(u.context,z,u))return u.global&&a.active--,R.reject(),R;if(z.aborted)return R.reject(),R;(l=x.clk)&&(y=l.name)&&!l.disabled&&(u.extraData=u.extraData||
{},u.extraData[y]=l.value,"image"==l.type&&(u.extraData[y+".x"]=x.clk_x,u.extraData[y+".y"]=x.clk_y));l=a("meta[name=csrf-token]").attr("content");(y=a("meta[name=csrf-param]").attr("content"))&&l&&(u.extraData=u.extraData||{},u.extraData[y]=l);u.forceSync?r():setTimeout(r,10);var ca,I,da=50,ba,ea=a.parseXML||function(C,D){window.ActiveXObject?(D=new ActiveXObject("Microsoft.XMLDOM"),D.async="false",D.loadXML(C)):D=(new DOMParser).parseFromString(C,"text/xml");return D&&D.documentElement&&"parsererror"!=
D.documentElement.nodeName?D:null},ha=a.parseJSON||function(C){return window.eval("("+C+")")},fa=function(C,D,J){var N=C.getResponseHeader("content-type")||"",Q="xml"===D||!D&&0<=N.indexOf("xml");C=Q?C.responseXML:C.responseText;Q&&"parsererror"===C.documentElement.nodeName&&a.error&&a.error("parsererror");J&&J.dataFilter&&(C=J.dataFilter(C,D));"string"===typeof C&&("json"===D||!D&&0<=N.indexOf("json")?C=ha(C):("script"===D||!D&&0<=N.indexOf("javascript"))&&a.globalEval(C));return C};return R}if(!this.length)return d("ajaxSubmit: skipping submit process - no element selected"),
this;var q=this;"function"==typeof g?g={success:g}:void 0===g&&(g={});var A=g.type||this.attr2("method");var B=g.url||this.attr2("action");(B=(B="string"===typeof B?a.trim(B):"")||window.location.href||"")&&(B=(B.match(/^([^#]+)/)||[])[1]);g=a.extend(!0,{url:B,success:a.ajaxSettings.success,type:A||a.ajaxSettings.type,iframeSrc:/^https/i.test(window.location.href||"")?"javascript:false":"about:blank"},g);B={};this.trigger("form-pre-serialize",[this,g,B]);if(B.veto)return d("ajaxSubmit: submit vetoed via form-pre-serialize trigger"),
this;if(g.beforeSerialize&&!1===g.beforeSerialize(this,g))return d("ajaxSubmit: submit aborted via beforeSerialize callback"),this;var G=g.traditional;void 0===G&&(G=a.ajaxSettings.traditional);var K=[],E=this.formToArray(g.semantic,K);if(g.data){g.extraData=g.data;var F=a.param(g.data,G)}if(g.beforeSubmit&&!1===g.beforeSubmit(E,this,g))return d("ajaxSubmit: submit aborted via beforeSubmit callback"),this;this.trigger("form-submit-validate",[E,this,g,B]);if(B.veto)return d("ajaxSubmit: submit vetoed via form-submit-validate trigger"),
this;B=a.param(E,G);F&&(B=B?B+"&"+F:F);"GET"==g.type.toUpperCase()?(g.url+=(0<=g.url.indexOf("?")?"&":"?")+B,g.data=null):g.data=B;var U=[];g.resetForm&&U.push(function(){q.resetForm()});g.clearForm&&U.push(function(){q.clearForm(g.includeHidden)});if(!g.dataType&&g.target){var aa=g.success||function(){};U.push(function(l){var p=g.replaceTarget?"replaceWith":"html";a(g.target)[p](l).each(aa,arguments)})}else g.success&&U.push(g.success);g.success=function(l,p,r){for(var w=g.context||this,x=0,y=U.length;x<
y;x++)U[x].apply(w,[l,p,r||q,q])};if(g.error){var X=g.error;g.error=function(l,p,r){X.apply(g.context||this,[l,p,r,q])}}if(g.complete){var h=g.complete;g.complete=function(l,p){h.apply(g.context||this,[l,p,q])}}F=0<a("input[type=file]:enabled",this).filter(function(){return""!==a(this).val()}).length;B="multipart/form-data"==q.attr("enctype")||"multipart/form-data"==q.attr("encoding");G=e&&f;d("fileAPI :"+G);var m;!1!==g.iframe&&(g.iframe||(F||B)&&!G)?g.closeKeepAlive?a.get(g.closeKeepAlive,function(){m=
t(E)}):m=t(E):m=(F||B)&&G?v(E):a.ajax(g);q.removeData("jqxhr").data("jqxhr",m);for(F=0;F<K.length;F++)K[F]=null;this.trigger("form-submit-notify",[this,g]);return this};a.fn.ajaxForm=function(g){g=g||{};g.delegation=g.delegation&&a.isFunction(a.fn.on);if(!g.delegation&&0===this.length){var n=this.selector,v=this.context;if(!a.isReady&&n)return d("DOM not ready, queuing ajaxForm"),a(function(){a(n,v).ajaxForm(g)}),this;d("terminating; zero elements found by selector"+(a.isReady?"":" (DOM not ready)"));
return this}return g.delegation?(a(document).off("submit.form-plugin",this.selector,b).off("click.form-plugin",this.selector,c).on("submit.form-plugin",this.selector,g,b).on("click.form-plugin",this.selector,g,c),this):this.ajaxFormUnbind().bind("submit.form-plugin",g,b).bind("click.form-plugin",g,c)};a.fn.ajaxFormUnbind=function(){return this.unbind("submit.form-plugin click.form-plugin")};a.fn.formToArray=function(g,n){var v=[];if(0===this.length)return v;var t=this[0],q=this.attr("id"),A=g?t.getElementsByTagName("*"):
t.elements;A&&!/MSIE [678]/.test(navigator.userAgent)&&(A=a(A).get());q&&(q=a(':input[form="'+q+'"]').get(),q.length&&(A=(A||[]).concat(q)));if(!A||!A.length)return v;var B,G;var K=0;for(G=A.length;K<G;K++){var E=A[K];if((q=E.name)&&!E.disabled)if(g&&t.clk&&"image"==E.type)t.clk==E&&(v.push({name:q,value:a(E).val(),type:E.type}),v.push({name:q+".x",value:t.clk_x},{name:q+".y",value:t.clk_y}));else if((B=a.fieldValue(E,!0))&&B.constructor==Array){n&&n.push(E);var F=0;for(E=B.length;F<E;F++)v.push({name:q,
value:B[F]})}else if(e&&"file"==E.type)if(n&&n.push(E),B=E.files,B.length)for(F=0;F<B.length;F++)v.push({name:q,value:B[F],type:E.type});else v.push({name:q,value:"",type:E.type});else null!==B&&"undefined"!=typeof B&&(n&&n.push(E),v.push({name:q,value:B,type:E.type,required:E.required}))}!g&&t.clk&&(g=a(t.clk),n=g[0],(q=n.name)&&!n.disabled&&"image"==n.type&&(v.push({name:q,value:g.val()}),v.push({name:q+".x",value:t.clk_x},{name:q+".y",value:t.clk_y})));return v};a.fn.formSerialize=function(g){return a.param(this.formToArray(g))};
a.fn.fieldSerialize=function(g){var n=[];this.each(function(){var v=this.name;if(v){var t=a.fieldValue(this,g);if(t&&t.constructor==Array)for(var q=0,A=t.length;q<A;q++)n.push({name:v,value:t[q]});else null!==t&&"undefined"!=typeof t&&n.push({name:this.name,value:t})}});return a.param(n)};a.fn.fieldValue=function(g){for(var n=[],v=0,t=this.length;v<t;v++){var q=a.fieldValue(this[v],g);null===q||"undefined"==typeof q||q.constructor==Array&&!q.length||(q.constructor==Array?a.merge(n,q):n.push(q))}return n};
a.fieldValue=function(g,n){var v=g.name,t=g.type,q=g.tagName.toLowerCase();void 0===n&&(n=!0);if(n&&(!v||g.disabled||"reset"==t||"button"==t||("checkbox"==t||"radio"==t)&&!g.checked||("submit"==t||"image"==t)&&g.form&&g.form.clk!=g||"select"==q&&-1==g.selectedIndex))return null;if("select"==q){q=g.selectedIndex;if(0>q)return null;n=[];g=g.options;v=(t="select-one"==t)?q+1:g.length;for(q=t?q:0;q<v;q++){var A=g[q];if(A.selected){var B=A.value;B||(B=A.attributes&&A.attributes.value&&!A.attributes.value.specified?
A.text:A.value);if(t)return B;n.push(B)}}return n}return a(g).val()};a.fn.clearForm=function(g){return this.each(function(){a("input,select,textarea",this).clearFields(g)})};a.fn.clearFields=a.fn.clearInputs=function(g){var n=/^(?:color|date|datetime|email|month|number|password|range|search|tel|text|time|url|week)$/i;return this.each(function(){var v=this.type,t=this.tagName.toLowerCase();n.test(v)||"textarea"==t?this.value="":"checkbox"==v||"radio"==v?this.checked=!1:"select"==t?this.selectedIndex=
-1:"file"==v?/MSIE/.test(navigator.userAgent)?a(this).replaceWith(a(this).clone(!0)):a(this).val(""):g&&(!0===g&&/hidden/.test(v)||"string"==typeof g&&a(this).is(g))&&(this.value="")})};a.fn.resetForm=function(){return this.each(function(){("function"==typeof this.reset||"object"==typeof this.reset&&!this.reset.nodeType)&&this.reset()})};a.fn.enable=function(g){void 0===g&&(g=!0);return this.each(function(){this.disabled=!g})};a.fn.selected=function(g){void 0===g&&(g=!0);return this.each(function(){var n=
this.type;"checkbox"==n||"radio"==n?this.checked=g:"option"==this.tagName.toLowerCase()&&(n=a(this).parent("select"),g&&n[0]&&"select-one"==n[0].type&&n.find("option").selected(!1),this.selected=g)})};a.fn.ajaxSubmit.debug=!1});(function(a){a.widget("ui.tagit",{options:{allowDuplicates:!1,caseSensitive:!0,fieldName:"tags",placeholderText:null,readOnly:!1,removeConfirmation:!1,tagLimit:null,availableTags:[],autocomplete:{},showAutocompleteOnFocus:!1,allowSpaces:!1,singleField:!1,singleFieldDelimiter:",",singleFieldNode:null,animate:!0,tabIndex:null,beforeTagAdded:null,afterTagAdded:null,beforeTagRemoved:null,afterTagRemoved:null,onTagClicked:null,onTagLimitExceeded:null,onTagAdded:null,onTagRemoved:null,tagSource:null},_create:function(){var b=
this;this.element.is("input")?(this.tagList=a("<ul></ul>").insertAfter(this.element),this.options.singleField=!0,this.options.singleFieldNode=this.element,this.element.addClass("tagit-hidden-field")):this.tagList=this.element.find("ul, ol").addBack().last();this.tagInput=a('<input type="text" />').addClass("ui-widget-content");this.options.readOnly&&this.tagInput.attr("disabled","disabled");this.options.tabIndex&&this.tagInput.attr("tabindex",this.options.tabIndex);this.options.placeholderText&&this.tagInput.attr("placeholder",
this.options.placeholderText);this.options.autocomplete.source||(this.options.autocomplete.source=function(f,k){var g=f.term.toLowerCase();f=a.grep(this.options.availableTags,function(n){return 0===n.toLowerCase().indexOf(g)});this.options.allowDuplicates||(f=this._subtractArray(f,this.assignedTags()));k(f)});this.options.showAutocompleteOnFocus&&(this.tagInput.focus(function(f,k){b._showAutocomplete()}),"undefined"===typeof this.options.autocomplete.minLength&&(this.options.autocomplete.minLength=
0));a.isFunction(this.options.autocomplete.source)&&(this.options.autocomplete.source=a.proxy(this.options.autocomplete.source,this));a.isFunction(this.options.tagSource)&&(this.options.tagSource=a.proxy(this.options.tagSource,this));this.tagList.addClass("tagit").addClass("ui-widget ui-widget-content ui-corner-all").append(a('<li class="tagit-new"></li>').append(this.tagInput)).click(function(f){var k=a(f.target);k.hasClass("tagit-label")?(k=k.closest(".tagit-choice"),k.hasClass("removed")||b._trigger("onTagClicked",
f,{tag:k,tagLabel:b.tagLabel(k)})):b.tagInput.focus()});var c=!1;if(this.options.singleField)if(this.options.singleFieldNode){var d=a(this.options.singleFieldNode),e=d.val().split(this.options.singleFieldDelimiter);d.val("");a.each(e,function(f,k){b.createTag(k,null,!0);c=!0})}else this.options.singleFieldNode=a('<input type="hidden" style="display:none;" value="" name="'+this.options.fieldName+'" />'),this.tagList.after(this.options.singleFieldNode);c||this.tagList.children("li").each(function(){a(this).hasClass("tagit-new")||
(b.createTag(a(this).text(),a(this).attr("class"),!0),a(this).remove())});this.tagInput.keydown(function(f){if(f.which==a.ui.keyCode.BACKSPACE&&""===b.tagInput.val()){var k=b._lastTag();!b.options.removeConfirmation||k.hasClass("remove")?b.removeTag(k):b.options.removeConfirmation&&k.addClass("remove ui-state-highlight")}else b.options.removeConfirmation&&b._lastTag().removeClass("remove ui-state-highlight");if(","===f.key&&!1===f.shiftKey||f.which===a.ui.keyCode.ENTER||f.which==a.ui.keyCode.TAB&&
""!==b.tagInput.val()||f.which==a.ui.keyCode.SPACE&&!0!==b.options.allowSpaces&&('"'!=a.trim(b.tagInput.val()).replace(/^s*/,"").charAt(0)||'"'==a.trim(b.tagInput.val()).charAt(0)&&'"'==a.trim(b.tagInput.val()).charAt(a.trim(b.tagInput.val()).length-1)&&0!==a.trim(b.tagInput.val()).length-1))f.which===a.ui.keyCode.ENTER&&""===b.tagInput.val()||f.preventDefault(),b.options.autocomplete.autoFocus&&b.tagInput.data("autocomplete-open")||(b.tagInput.autocomplete("close"),b.createTag(b._cleanedInput()))}).blur(function(f){b.tagInput.data("autocomplete-open")||
b.createTag(b._cleanedInput())});if(this.options.availableTags||this.options.tagSource||this.options.autocomplete.source)d={select:function(f,k){b.createTag(k.item.value);return!1}},a.extend(d,this.options.autocomplete),d.source=this.options.tagSource||d.source,this.tagInput.autocomplete(d).bind("autocompleteopen.tagit",function(f,k){b.tagInput.data("autocomplete-open",!0)}).bind("autocompleteclose.tagit",function(f,k){b.tagInput.data("autocomplete-open",!1)}),this.tagInput.autocomplete("widget").addClass("tagit-autocomplete")},
destroy:function(){a.Widget.prototype.destroy.call(this);this.element.unbind(".tagit");this.tagList.unbind(".tagit");this.tagInput.removeData("autocomplete-open");this.tagList.removeClass("tagit ui-widget ui-widget-content ui-corner-all tagit-hidden-field");this.element.is("input")?(this.element.removeClass("tagit-hidden-field"),this.tagList.remove()):(this.element.children("li").each(function(){a(this).hasClass("tagit-new")?a(this).remove():(a(this).removeClass("tagit-choice ui-widget-content ui-state-default ui-state-highlight ui-corner-all remove tagit-choice-editable tagit-choice-read-only"),
a(this).text(a(this).children(".tagit-label").text()))}),this.singleFieldNode&&this.singleFieldNode.remove());return this},_cleanedInput:function(){return a.trim(this.tagInput.val().replace(/^"(.*)"$/,"$1"))},_lastTag:function(){return this.tagList.find(".tagit-choice:last:not(.removed)")},_tags:function(){return this.tagList.find(".tagit-choice:not(.removed)")},assignedTags:function(){var b=this,c=[];this.options.singleField?(c=a(this.options.singleFieldNode).val().split(this.options.singleFieldDelimiter),
""===c[0]&&(c=[])):this._tags().each(function(){c.push(b.tagLabel(this))});return c},_updateSingleTagsField:function(b){a(this.options.singleFieldNode).val(b.join(this.options.singleFieldDelimiter)).trigger("change")},_subtractArray:function(b,c){for(var d=[],e=0;e<b.length;e++)-1==a.inArray(b[e],c)&&d.push(b[e]);return d},tagLabel:function(b){return this.options.singleField?a(b).find(".tagit-label:first").text():a(b).find("input:first").val()},_showAutocomplete:function(){this.tagInput.autocomplete("search",
"")},_findTagByLabel:function(b){var c=this,d=null;this._tags().each(function(e){if(c._formatStr(b)==c._formatStr(c.tagLabel(this)))return d=a(this),!1});return d},_isNew:function(b){return!this._findTagByLabel(b)},_formatStr:function(b){return this.options.caseSensitive?b:a.trim(b.toLowerCase())},_effectExists:function(b){return!(!a.effects||!(a.effects[b]||a.effects.effect&&a.effects.effect[b]))},createTag:function(b,c,d){var e=this;b=a.trim(b);this.options.preprocessTag&&(b=this.options.preprocessTag(b));
if(""===b)return!1;if(!this.options.allowDuplicates&&!this._isNew(b))return b=this._findTagByLabel(b),!1!==this._trigger("onTagExists",null,{existingTag:b,duringInitialization:d})&&this._effectExists("highlight")&&b.effect("highlight"),!1;if(this.options.tagLimit&&this._tags().length>=this.options.tagLimit)return this._trigger("onTagLimitExceeded",null,{duringInitialization:d}),!1;var f=a(this.options.onTagClicked?'<a class="tagit-label"></a>':'<span class="tagit-label"></span>').text(b),k=a("<li></li>").addClass("tagit-choice ui-widget-content ui-state-default ui-corner-all").addClass(c).append(f);
this.options.readOnly?k.addClass("tagit-choice-read-only"):(k.addClass("tagit-choice-editable"),c=a("<span></span>").addClass("ui-icon ui-icon-close"),c=a('<a><span class="text-icon">\u00d7</span></a>').addClass("tagit-close").append(c).click(function(g){e.removeTag(k)}),k.append(c));this.options.singleField||(f=f.html(),k.append('<input type="hidden" value="'+f+'" name="'+this.options.fieldName+'" class="tagit-hidden-field" />'));!1!==this._trigger("beforeTagAdded",null,{tag:k,tagLabel:this.tagLabel(k),
duringInitialization:d})&&(this.options.singleField&&(f=this.assignedTags(),f.push(b),this._updateSingleTagsField(f)),this._trigger("onTagAdded",null,k),this.tagInput.val(""),this.tagInput.parent().before(k),this._trigger("afterTagAdded",null,{tag:k,tagLabel:this.tagLabel(k),duringInitialization:d}),this.options.showAutocompleteOnFocus&&!d&&setTimeout(function(){e._showAutocomplete()},0))},removeTag:function(b,c){c="undefined"===typeof c?this.options.animate:c;b=a(b);this._trigger("onTagRemoved",
null,b);if(!1!==this._trigger("beforeTagRemoved",null,{tag:b,tagLabel:this.tagLabel(b)})){if(this.options.singleField){var d=this.assignedTags(),e=this.tagLabel(b);d=a.grep(d,function(k){return k!=e});this._updateSingleTagsField(d)}if(c){b.addClass("removed");c=this._effectExists("blind")?["blind",{direction:"horizontal"},"fast"]:["fast"];var f=this;c.push(function(){b.remove();f._trigger("afterTagRemoved",null,{tag:b,tagLabel:f.tagLabel(b)})});b.fadeOut("fast").hide.apply(b,c).dequeue()}else b.remove(),
this._trigger("afterTagRemoved",null,{tag:b,tagLabel:this.tagLabel(b)})}},removeTagByLabel:function(b,c){var d=this._findTagByLabel(b);if(!d)throw"No such tag exists with the name '"+b+"'";this.removeTag(d,c)},removeAll:function(){var b=this;this._tags().each(function(c,d){b.removeTag(d,!1)})}})})(jQuery);jQuery.fn.sortElements=function(){var a=[].sort;return function(b,c){c=c||function(){return this};var d=this.map(function(){var e=c.call(this),f=e.parentNode,k=f.insertBefore(document.createTextNode(""),e.nextSibling);return function(){if(f===this)throw Error("You can't sort elements if any one is a descendant of another.");f.insertBefore(this,k);f.removeChild(k)}});return a.call(this,b).each(function(e){d[e].call(c.call(this))})}}();/*
 jQuery Cookie Plugin v1.4.0
 https://github.com/carhartl/jquery-cookie

 Copyright 2013 Klaus Hartl
 Released under the MIT license
*/
(function(a){"function"===typeof define&&define.amd?define(["jquery"],a):a(jQuery)})(function(a){function b(f){f=e.json?JSON.stringify(f):String(f);return e.raw?f:encodeURIComponent(f)}function c(f,k){if(e.raw)var g=f;else a:{0===f.indexOf('"')&&(f=f.slice(1,-1).replace(/\\"/g,'"').replace(/\\\\/g,"\\"));try{f=decodeURIComponent(f.replace(d," "))}catch(n){g=void 0;break a}try{g=e.json?JSON.parse(f):f;break a}catch(n){}g=void 0}return a.isFunction(k)?k(g):g}var d=/\+/g,e=a.cookie=function(f,k,g){if(void 0!==
k&&!a.isFunction(k)){g=a.extend({},e.defaults,g);if("number"===typeof g.expires){var n=g.expires,v=g.expires=new Date;v.setDate(v.getDate()+n)}return document.cookie=[e.raw?f:encodeURIComponent(f),"=",b(k),g.expires?"; expires="+g.expires.toUTCString():"",g.path?"; path="+g.path:"",g.domain?"; domain="+g.domain:"",g.secure?"; secure":""].join("")}g=f?void 0:{};n=document.cookie?document.cookie.split("; "):[];v=0;for(var t=n.length;v<t;v++){var q=n[v].split("=");var A=q.shift();A=e.raw?A:decodeURIComponent(A);
q=q.join("=");if(f&&f===A){g=c(q,k);break}f||void 0===(q=c(q))||(g[A]=q)}return g};e.defaults={};a.removeCookie=function(f,k){return void 0!==a.cookie(f)?(a.cookie(f,"",a.extend({},k,{expires:-1})),!0):!1}});(function(a){a.fn.equalizeElementHeights=function(){var b=this.map(function(c,d){return a(d).height()}).get();return this.height(Math.max.apply(this,b))}})(jQuery);jQuery&&function(a){a.extend(a.fn,{selectBox:function(b,c){var d,e="",f=navigator.platform.match(/mac/i),k=function(h,m){if(navigator.userAgent.match(/iPad|iPhone|Android|IEMobile|BlackBerry/i)||"select"!==h.tagName.toLowerCase())return!1;h=a(h);if(h.data("selectBox-control"))return!1;var l=a('<a class="selectBox" />'),p=h.attr("multiple")||1<parseInt(h.attr("size"));m=m||{};l.width(h.outerWidth()).addClass(h.attr("class")).attr("title",h.attr("title")||"").attr("tabindex",parseInt(h.attr("tabindex"))).css("display",
"inline-block").bind("focus.selectBox",function(){this!==document.activeElement&&document.body!==document.activeElement&&a(document.activeElement).blur();l.hasClass("selectBox-active")||(l.addClass("selectBox-active"),h.trigger("focus"))}).bind("blur.selectBox",function(){l.hasClass("selectBox-active")&&(l.removeClass("selectBox-active"),h.trigger("blur"))});a(window).data("selectBox-bindings")||a(window).data("selectBox-bindings",!0).bind("scroll.selectBox",A).bind("resize.selectBox",A);h.attr("disabled")&&
l.addClass("selectBox-disabled");h.bind("click.selectBox",function(y){l.focus();y.preventDefault()});if(p){var r=g(h,"inline");l.append(r).data("selectBox-options",r).addClass("selectBox-inline selectBox-menuShowing").bind("keydown.selectBox",function(y){F(h,y)}).bind("keypress.selectBox",function(y){U(h,y)}).bind("mousedown.selectBox",function(y){a(y.target).is("A.selectBox-inline")&&y.preventDefault();l.hasClass("selectBox-focus")||l.focus()}).insertAfter(h);if(!h[0].style.height){p=h.attr("size")?
parseInt(h.attr("size")):5;var w=l.clone().removeAttr("id").css({position:"absolute",top:"-9999em"}).show().appendTo("body");w.find(".selectBox-options").html("<li><a>\u00a0</a></li>");var x=parseInt(w.find(".selectBox-options A:first").html("&nbsp;").outerHeight());w.remove();l.height(x*p)}}else p=a('<span class="selectBox-label" />'),w=a('<span class="selectBox-arrow" />'),p.attr("class",n(h)).text(v(h)),r=g(h,"dropdown"),r.appendTo("BODY"),l.data("selectBox-options",r).addClass("selectBox-dropdown").append(p).append(w).bind("mousedown.selectBox",
function(y){l.hasClass("selectBox-menuShowing")?A():(y.stopPropagation(),r.data("selectBox-down-at-x",y.screenX).data("selectBox-down-at-y",y.screenY),q(h))}).bind("keydown.selectBox",function(y){F(h,y)}).bind("keypress.selectBox",function(y){U(h,y)}).bind("open.selectBox",function(y,H){H&&!0===H._selectBox||q(h)}).bind("close.selectBox",function(y,H){H&&!0===H._selectBox||A()}).insertAfter(h),w=l.width()-w.outerWidth()-parseInt(p.css("paddingLeft"))-parseInt(p.css("paddingLeft")),p.width(w);X(l);
h.addClass("selectBox").data("selectBox-control",l).data("selectBox-settings",m).hide()},g=function(h,m){var l=function(w,x){w.children("OPTION, OPTGROUP").each(function(){if(a(this).is("OPTION"))if(0<a(this).length){var y=a(this),H=x,L=a("<li />"),M=a("<a />");L.addClass(y.attr("class"));L.data(y.data());M.attr("rel",y.val()).text(y.text());L.append(M);y.attr("disabled")&&L.addClass("selectBox-disabled");y.attr("selected")&&L.addClass("selectBox-selected");H.append(L)}else x.append("<li>\u00a0</li>");
else y=a('<li class="selectBox-optgroup" />'),y.text(a(this).attr("label")),x.append(y),x=l(a(this),x)});return x};switch(m){case "inline":var p=a('<ul class="selectBox-options" />');p=l(h,p);p.find("A").bind("mouseover.selectBox",function(w){G(h,a(this).parent())}).bind("mouseout.selectBox",function(w){K(h,a(this).parent())}).bind("mousedown.selectBox",function(w){w.preventDefault();h.selectBox("control").hasClass("selectBox-active")||h.selectBox("control").focus()}).bind("mouseup.selectBox",function(w){A();
B(h,a(this).parent(),w)});X(p);return p;case "dropdown":p=a('<ul class="selectBox-dropdown-menu selectBox-options" />');p=l(h,p);p.data("selectBox-select",h).css("display","none").appendTo("BODY").find("A").bind("mousedown.selectBox",function(w){w.preventDefault();w.screenX===p.data("selectBox-down-at-x")&&w.screenY===p.data("selectBox-down-at-y")&&(p.removeData("selectBox-down-at-x").removeData("selectBox-down-at-y"),A())}).bind("mouseup.selectBox",function(w){if(w.screenX!==p.data("selectBox-down-at-x")||
w.screenY!==p.data("selectBox-down-at-y"))p.removeData("selectBox-down-at-x").removeData("selectBox-down-at-y"),B(h,a(this).parent()),A()}).bind("mouseover.selectBox",function(w){G(h,a(this).parent())}).bind("mouseout.selectBox",function(w){K(h,a(this).parent())});m=h.attr("class")||"";if(""!==m){m=m.split(" ");for(var r in m)p.addClass(m[r]+"-selectBox-dropdown-menu")}X(p);return p}},n=function(h){return("selectBox-label "+(a(h).find("OPTION:selected").attr("class")||"")).replace(/\s+$/,"")},v=function(h){return a(h).find("OPTION:selected").text()||
"\u00a0"},t=function(h){h=a(h);var m=h.data("selectBox-control");m&&m.find(".selectBox-label").attr("class",n(h)).text(v(h))},q=function(h){h=a(h);var m=h.data("selectBox-control"),l=h.data("selectBox-settings"),p=m.data("selectBox-options");if(m.hasClass("selectBox-disabled"))return!1;A();var r=isNaN(m.css("borderBottomWidth"))?0:parseInt(m.css("borderBottomWidth"));p.width(m.innerWidth()).css({top:m.offset().top+m.outerHeight()-r,left:m.offset().left});if(h.triggerHandler("beforeopen"))return!1;
r=function(){h.triggerHandler("open",{_selectBox:!0})};switch(l.menuTransition){case "fade":p.fadeIn(l.menuSpeed,r);break;case "slide":p.slideDown(l.menuSpeed,r);break;default:p.show(l.menuSpeed,r)}l.menuSpeed||r();l=p.find(".selectBox-selected:first");E(h,l,!0);G(h,l);m.addClass("selectBox-menuShowing");a(document).bind("mousedown.selectBox",function(w){a(w.target).parents().andSelf().hasClass("selectBox-options")||A()})},A=function(){0!==a(".selectBox-dropdown-menu:visible").length&&(a(document).unbind("mousedown.selectBox"),
a(".selectBox-dropdown-menu").each(function(){var h=a(this),m=h.data("selectBox-select"),l=m.data("selectBox-control"),p=m.data("selectBox-settings");if(m.triggerHandler("beforeclose"))return!1;var r=function(){m.triggerHandler("close",{_selectBox:!0})};if(p){switch(p.menuTransition){case "fade":h.fadeOut(p.menuSpeed,r);break;case "slide":h.slideUp(p.menuSpeed,r);break;default:h.hide(p.menuSpeed,r)}p.menuSpeed||r();l.removeClass("selectBox-menuShowing")}else a(this).hide(),a(this).triggerHandler("close",
{_selectBox:!0}),a(this).removeClass("selectBox-menuShowing")}))},B=function(h,m,l){h=a(h);m=a(m);var p=h.data("selectBox-control");h.data("selectBox-settings");if(p.hasClass("selectBox-disabled")||0===m.length||m.hasClass("selectBox-disabled"))return!1;h.attr("multiple")?l.shiftKey&&p.data("selectBox-last-selected")?(m.toggleClass("selectBox-selected"),l=m.index()>p.data("selectBox-last-selected").index()?m.siblings().slice(p.data("selectBox-last-selected").index(),m.index()):m.siblings().slice(m.index(),
p.data("selectBox-last-selected").index()),l=l.not(".selectBox-optgroup, .selectBox-disabled"),m.hasClass("selectBox-selected")?l.addClass("selectBox-selected"):l.removeClass("selectBox-selected")):f&&l.metaKey||!f&&l.ctrlKey?m.toggleClass("selectBox-selected"):(m.siblings().removeClass("selectBox-selected"),m.addClass("selectBox-selected")):(m.siblings().removeClass("selectBox-selected"),m.addClass("selectBox-selected"));p.hasClass("selectBox-dropdown")&&p.find(".selectBox-label").text(m.text());
var r=0,w=[];h.attr("multiple")?p.find(".selectBox-selected A").each(function(){w[r++]=a(this).attr("rel")}):w=m.find("A").attr("rel");p.data("selectBox-last-selected",m);h.val()!==w&&(h.val(w),t(h),h.trigger("change"));return!0},G=function(h,m){h=a(h);m=a(m);h.data("selectBox-control").data("selectBox-options").find(".selectBox-hover").removeClass("selectBox-hover");m.addClass("selectBox-hover")},K=function(h,m){h=a(h);a(m);h.data("selectBox-control").data("selectBox-options").find(".selectBox-hover").removeClass("selectBox-hover")},
E=function(h,m,l){if(m&&0!==m.length){h=a(h);h=h.data("selectBox-control");var p=h.data("selectBox-options");h=h.hasClass("selectBox-dropdown")?p:p.parent();p=parseInt(m.offset().top-h.position().top);var r=parseInt(p+m.outerHeight());l?h.scrollTop(m.offset().top-h.offset().top+h.scrollTop()-h.height()/2):(0>p&&h.scrollTop(m.offset().top-h.offset().top+h.scrollTop()),r>h.height()&&h.scrollTop(m.offset().top+m.outerHeight()-h.offset().top+h.scrollTop()-h.height()))}},F=function(h,m){h=a(h);var l=h.data("selectBox-control"),
p=l.data("selectBox-options"),r=h.data("selectBox-settings"),w;if(!l.hasClass("selectBox-disabled"))switch(m.keyCode){case 8:m.preventDefault();e="";break;case 9:case 27:A();K(h);break;case 13:l.hasClass("selectBox-menuShowing")?(B(h,p.find("LI.selectBox-hover:first"),m),l.hasClass("selectBox-dropdown")&&A()):q(h);break;case 38:case 37:m.preventDefault();if(l.hasClass("selectBox-menuShowing")){var x=p.find(".selectBox-hover").prev("LI");l=p.find("LI:not(.selectBox-optgroup)").length;for(w=0;(0===
x.length||x.hasClass("selectBox-disabled")||x.hasClass("selectBox-optgroup"))&&!(x=x.prev("LI"),0===x.length&&(x=r.loopOptions?p.find("LI:last"):p.find("LI:first")),++w>=l););G(h,x);B(h,x,m);E(h,x)}else q(h);break;case 40:case 39:if(m.preventDefault(),l.hasClass("selectBox-menuShowing")){x=p.find(".selectBox-hover").next("LI");l=p.find("LI:not(.selectBox-optgroup)").length;for(w=0;(0===x.length||x.hasClass("selectBox-disabled")||x.hasClass("selectBox-optgroup"))&&!(x=x.next("LI"),0===x.length&&(x=
r.loopOptions?p.find("LI:first"):p.find("LI:last")),++w>=l););G(h,x);B(h,x,m);E(h,x)}else q(h)}},U=function(h,m){h=a(h);var l=h.data("selectBox-control"),p=l.data("selectBox-options");if(!l.hasClass("selectBox-disabled"))switch(m.keyCode){case 9:case 27:case 13:case 38:case 37:case 40:case 39:break;default:l.hasClass("selectBox-menuShowing")||q(h),m.preventDefault(),clearTimeout(d),e+=String.fromCharCode(m.charCode||m.keyCode),p.find("A").each(function(){if(a(this).text().substr(0,e.length).toLowerCase()===
e.toLowerCase())return G(h,a(this).parent()),E(h,a(this).parent()),!1}),d=setTimeout(function(){e=""},1E3)}},aa=function(h,m){h=a(h);h.val(m);m=h.val();null===m&&(m=h.children().first().val(),h.val(m));var l=h.data("selectBox-control");if(l){var p=h.data("selectBox-settings");l=l.data("selectBox-options");t(h);l.find(".selectBox-selected").removeClass("selectBox-selected");l.find("A").each(function(){if("object"===typeof m)for(var r=0;r<m.length;r++)a(this).attr("rel")==m[r]&&a(this).parent().addClass("selectBox-selected");
else a(this).attr("rel")==m&&a(this).parent().addClass("selectBox-selected")});p.change&&p.change.call(h)}},X=function(h){a(h).css("MozUserSelect","none").bind("selectstart",function(m){m.preventDefault()})};switch(b){case "control":return a(this).data("selectBox-control");case "settings":if(!c)return a(this).data("selectBox-settings");a(this).each(function(){a(this).data("selectBox-settings",a.extend(!0,a(this).data("selectBox-settings"),c))});break;case "options":if(void 0===c)return a(this).data("selectBox-control").data("selectBox-options");
a(this).each(function(){var h=a(this);var m=h.data("selectBox-control");h.data("selectBox-settings");switch(typeof c){case "string":h.html(c);break;case "object":for(r in h.html(""),c)if(null!==c[r])if("object"===typeof c[r]){var l=a('<optgroup label="'+r+'" />');for(p in c[r])l.append('<option value="'+p+'">'+c[r][p]+"</option>");h.append(l)}else l=a('<option value="'+r+'">'+c[r]+"</option>"),h.append(l)}if(m){m.data("selectBox-options").remove();var p=m.hasClass("selectBox-dropdown")?"dropdown":
"inline";var r=g(h,p);m.data("selectBox-options",r);switch(p){case "inline":m.append(r);break;case "dropdown":t(h),a("BODY").append(r)}}});break;case "value":if(void 0===c)return a(this).val();a(this).each(function(){aa(this,c)});break;case "refresh":a(this).each(function(){var h=a(this);h.selectBox("options",h.html())});break;case "enable":a(this).each(function(){var h=a(this);h.attr("disabled",!1);(h=h.data("selectBox-control"))&&h.removeClass("selectBox-disabled")});break;case "disable":a(this).each(function(){var h=
a(this);h.attr("disabled",!0);(h=h.data("selectBox-control"))&&h.addClass("selectBox-disabled")});break;case "destroy":a(this).each(function(){var h=a(this);var m=h.data("selectBox-control");m&&(m.data("selectBox-options").remove(),m.remove(),h.removeClass("selectBox").removeData("selectBox-control").data("selectBox-control",null).removeData("selectBox-settings").data("selectBox-settings",null).show())});break;default:a(this).each(function(){k(this,b)})}return a(this)}})}(jQuery);(function(a){a.pkp=a.pkp||{};a.pkp.classes=a.pkp.classes||{};a.pkp.controllers=a.pkp.controllers||{};a.pkp.plugins=a.pkp.plugins||{};a.pkp.plugins.blocks=a.pkp.plugins.blocks||{};a.pkp.plugins.generic=a.pkp.plugins.generic||{};a.pkp.plugins.pubIds=a.pkp.plugins.pubIds||{};a.pkp.plugins.importexport=a.pkp.plugins.importexport||{};a.pkp.classes.Helper=function(){throw Error("Trying to instantiate the Helper singleton!");};a.pkp.classes.Helper.CHARS_="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");
a.pkp.classes.Helper.uuid=function(){var b=a.pkp.classes.Helper.CHARS_,c=Array(36),d=0,e;for(e=0;36>e;e++)if(8==e||13==e||18==e||23==e)c[e]="-";else if(14==e)c[e]="4";else{2>=d&&(d=33554432+16777216*Math.random()|0);var f=d&15;d>>=4;c[e]=b[19==e?f&3|8:f]}return c.join("")};a.pkp.classes.Helper.inherits=function(b,c){var d=function(){};d.prototype=c.prototype;b.parent_=c.prototype;b.prototype=new d;b.prototype.constructor=b;c.prototype.constructor==Object.prototype.constructor&&(c.prototype.constructor=
c)};a.pkp.classes.Helper.objectFactory=function(b,c){var d=a.pkp.classes.Helper.resolveObjectName(b);var e=a.pkp.classes.Helper.getObjectProxyInstance();a.extend(!0,e,a.pkp.classes.ObjectProxy);a.pkp.classes.Helper.inherits(e,d);a.extend(!0,e.prototype,a.pkp.classes.ObjectProxy.prototype);return new e(b,c)};a.pkp.classes.Helper.resolveObjectName=function(b){var c;var d=b.split(".");if("$"!=d.shift())throw Error(['Namespace "',d[0],'" for object "',b,'" is currently not supported!'].join(""));var e=
d[d.length-1];if(e.charAt(0).toUpperCase()!==e.charAt(0))throw Error(['The name "',b,'" does not point to aconstructor which must always be upper case!'].join(""));e=a;for(c in d)if(e=e[d[c]],void 0===e)throw Error(['Constructor for object "',b,'" not found!'].join(""));if(!a.isFunction(e))throw Error(['The name "',b,'" does not point to a,constructor which must always be a function!'].join());return e};a.pkp.classes.Helper.getObjectProxyInstance=function(){var b=function(c,d){this.objectName_=c;
this.parent.apply(this,d)};b.objectName_="";b.prototype.parent=function(c,d){};return b};a.pkp.classes.Helper.injectMixin=function(b,c){c=a.pkp.classes.Helper.objectFactory(c,[]);a.extend(!0,b,c)};a.pkp.classes.Helper.curry=function(b,c,d){if(2<arguments.length){var e;var f=Array.prototype.slice.call(arguments,2);return function(){e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,f);return b.apply(c,e)}}return function(){return b.apply(c,arguments)}};a.pkp.classes.Helper.escapeJQuerySelector=
function(b){return b.replace("@","\\@")}})(jQuery);(function(a){a.pkp.classes.ObjectProxy=function(){};a.pkp.classes.ObjectProxy.prototype.objectName_="";a.pkp.classes.ObjectProxy.prototype.self=function(b,c){var d;for(d=this.constructor;d;d=d.parent_&&d.parent_.constructor)if(d.hasOwnProperty(b)){d=d[b];if(a.isFunction(d)){var e=Array.prototype.slice.call(arguments,1);return d.apply(this,e)}return d}throw Error(['Static property "',b,'" not found!'].join(""));};a.pkp.classes.ObjectProxy.prototype.parent=function(b,c){var d;var e=a.pkp.classes.ObjectProxy.prototype.parent.caller;
if(e.parent_){var f=Array.prototype.slice.call(arguments);return e.parent_.constructor.apply(this,f)}f=Array.prototype.slice.call(arguments,1);if(this.hasOwnProperty(b)&&this[b]===e)return this.constructor.parent_[b].apply(this,f);var k=!1;for(d=this.constructor;d;d=d.parent_&&d.parent_.constructor)if(d.prototype.hasOwnProperty(b)&&d.prototype[b]===e)k=!0;else if(k)return d.prototype[b].apply(this,f);throw Error("Trying to call parent from a method of one name to a method of a different name");};
a.pkp.classes.ObjectProxy.prototype.getObjectName=function(){return this.objectName_}})(jQuery);(function(a){a.pkp.classes.Handler=function(b,c){if(1<b.length)throw Error("jQuery selector contained more than one handler!");this.$htmlElement_=b;if(void 0!==this.data("handler"))throw Error(['The handler "',this.getObjectName(),'" has already been bound to the selected element!'].join(""));this.eventBindings_={};this.dataItems_={};this.publishedEvents_={};this.handlerChildren_=[];this.globalEventListeners_={};var d=this.$htmlElement_.parents();var e=this;d.each(function(f){a.pkp.classes.Handler.hasHandler(a(d[f]))&&
a.pkp.classes.Handler.getHandler(a(d[f])).handlerChildren_.push(e)});c.eventBridge&&(this.eventBridge_=c.eventBridge);if(c.publishChangeEvents)for(this.publishChangeEvents_=c.publishChangeEvents,b=0;b<this.publishChangeEvents_.length;b++)this.publishEvent(this.publishChangeEvents_[b]);else this.publishChangeEvents_=[];this.data("handler",this)};a.pkp.classes.Handler.prototype.publishChangeEvents_=null;a.pkp.classes.Handler.prototype.$htmlElement_=null;a.pkp.classes.Handler.prototype.eventBindings_=
null;a.pkp.classes.Handler.prototype.dataItems_=null;a.pkp.classes.Handler.prototype.publishedEvents_=null;a.pkp.classes.Handler.prototype.eventBridge_=null;a.pkp.classes.Handler.prototype.globalEventListeners_=null;a.pkp.classes.Handler.getHandler=function(b){b=b.data("pkp.handler");if(!(b instanceof a.pkp.classes.Handler))throw Error("There is no handler bound to this element!");return b};a.pkp.classes.Handler.hasHandler=function(b){return b.data("pkp.handler")instanceof a.pkp.classes.Handler};
a.pkp.classes.Handler.prototype.getHtmlElement=function(){a.pkp.classes.Handler.checkContext_(this);return this.$htmlElement_};a.pkp.classes.Handler.prototype.publishChangeEvents=function(){var b;for(b=0;b<this.publishChangeEvents_.length;b++)this.trigger(this.publishChangeEvents_[b])};a.pkp.classes.Handler.prototype.handleEvent=function(b){var c;var d=a(this);var e=a.pkp.classes.Handler.getHandler(d);if(d[0]!==e.getHtmlElement.call(e)[0])throw Error("An invalid handler is bound to the calling element of an event!");
d=e.eventBindings_[b.type];if(void 0===d)return!1;var f=a.makeArray(arguments);var k=!0;f.unshift(this);var g=0;for(c=d.length;g<c&&(!1===d[g].apply(e,f)&&(k=!1),!b.isImmediatePropagationStopped());g++);b.stopPropagation();return k};a.pkp.classes.Handler.prototype.callbackWrapper=function(b,c){a.pkp.classes.Handler.checkContext_(this);c||(c=this);return function(){var d=a.makeArray(arguments);d.unshift(this);return b.apply(c,d)}};a.pkp.classes.Handler.prototype.remoteResponse=function(b,c){return this.handleJson(c)};
a.pkp.classes.Handler.prototype.remove=function(){a.pkp.classes.Handler.checkContext_(this);var b;var c=this.getHtmlElement();c.unbind(".pkpHandler");this.unbindGlobalAll();for(b in this.dataItems_)"pkp.handler"!==b&&c.removeData(b);c.trigger("pkpRemoveHandler");c.unbind(".pkpHandlerRemove");c.removeData("pkp.handler")};a.pkp.classes.Handler.prototype.handleJson=function(b){var c;if(!b)throw Error("Server error: Server returned no or invalid data!");if(!0===b.status){for(c in b.events){var d=b.events[c].hasOwnProperty("data")?
b.events[c].data:null;null!==d&&d.isGlobalEvent?(d.handler=this,pkp.eventBus.$emit(b.events[c].name,d)):this.trigger(b.events[c].name,d)}return b}b.content&&alert(b.content);return!1};a.pkp.classes.Handler.prototype.setHtmlElement=function(b){a.pkp.classes.Handler.checkContext_(this);return this.$htmlElement_=b};a.pkp.classes.Handler.prototype.bind=function(b,c){a.pkp.classes.Handler.checkContext_(this);if(!this.eventBindings_[b]){this.eventBindings_[b]=[];var d=".pkpHandler";"pkpRemoveHandler"===
b&&(d=".pkpHandlerRemove");this.getHtmlElement().bind(b+d,this.handleEvent)}this.eventBindings_[b].push(c)};a.pkp.classes.Handler.prototype.unbind=function(b,c){a.pkp.classes.Handler.checkContext_(this);if(!this.eventBindings_[b])return!1;var d;var e=0;for(d=this.eventBindings_[b].length;e<d;e++)if(this.eventBindings_[b][e]===c){this.eventBindings_[b].splice([e],1);break}0===this.eventBindings_[b].length&&(delete this.eventBindings_[b],this.getHtmlElement().unbind(b,this.handleEvent));return!0};a.pkp.classes.Handler.prototype.bindGlobal=
function(b,c){"undefined"===typeof this.globalEventListeners_[b]&&(this.globalEventListeners_[b]=[]);c=this.callbackWrapper(c);this.globalEventListeners_[b].push(c);pkp.eventBus.$on(b,c)};a.pkp.classes.Handler.prototype.unbindGlobal=function(b,c){var d=this.callbackWrapper(c),e=[];"undefined"!==typeof this.globalEventListeners_[b]&&(this.globalEventListeners.forEach(function(f){f!==d&&e.push(f)}),this.globalEventListeners=e);pkp.eventBus.$off(b,d)};a.pkp.classes.Handler.prototype.unbindGlobalAll=
function(){var b,c;if("undefined"!==typeof this.globalEventListeners_)for(b in this.globalEventListeners_)for(c in this.globalEventListeners_[b])pkp.eventBus.$off(b,this.globalEventListeners_[b][c]);this.globalEventListeners=null;this.unbindGlobalChildren()};a.pkp.classes.Handler.prototype.unbindGlobalChildren=function(){this.handlerChildren_.forEach(function(b){"undefined"!==typeof b.unbindGlobalAll?b.unbindGlobalAll():"undefined"!==typeof b.$destroy&&(delete pkp.registry._instances[b.id],b.$destroy())})};
a.pkp.classes.Handler.prototype.data=function(b,c){a.pkp.classes.Handler.checkContext_(this);b="pkp."+b;void 0!==c&&(this.dataItems_[b]=!0);return 1<arguments.length?this.getHtmlElement().data(b,c):this.getHtmlElement().data(b)};a.pkp.classes.Handler.prototype.trigger=function(b,c){void 0===c&&(c=null);this.getHtmlElement().triggerHandler(b,c);this.publishedEvents_[b]||this.triggerPublicEvent_(b,c)};a.pkp.classes.Handler.prototype.publishEvent=function(b){this.publishedEvents_[b]||(this.publishedEvents_[b]=
!0,this.bind(b,function(c,d,e){var f=null;2<arguments.length&&(f=Array.prototype.slice.call(arguments,2));this.triggerPublicEvent_(b,f)}))};a.pkp.classes.Handler.prototype.switchViz=function(b){a(b.currentTarget).parent().parent().find("span").toggle()};a.pkp.classes.Handler.prototype.initializeTinyMCE=function(){if("undefined"!==typeof tinyMCE){var b=this.getHtmlElement().attr("id"),c=a.pkp.controllers.SiteHandler.prototype.tinymceParams_;c.defaultToolbar=c.toolbar;a("#"+b).find(".richContent").each(function(){var d=
a(this).attr("id"),e=a("<div></div>"),f=a("<div></div>"),k=a.pkp.controllers.SiteHandler.prototype.tinymceParams_;a(this).hasClass("extendedRichContent")?k.toolbar=k.richToolbar:k.toolbar=k.defaultToolbar;tinyMCE.EditorManager.createEditor(d,k).render();if(a(this).hasClass("localizable")||a(this).hasClass("flag"))if(e.addClass("mceLocalizationIcon localizable"),e.attr("id","mceLocalizationIcon-"+d),a(this).wrap(f),a(this).parent().append(e),a(this).hasClass("localizable"))e.addClass("mceGlobe");else if(a(this).hasClass("flag")&&
(d=a(this).attr("class").split(" "),d.length))for(f=0;f<d.length;f++)if(d[f].match(/^flag_[a-z]{2}_[A-Z]{2}$/)){e.addClass(d[f]);break}})}};a.pkp.classes.Handler.prototype.triggerPublicEvent_=function(b,c){this.getHtmlElement().parent().trigger(b,c);this.eventBridge_&&a('[id^="'+this.eventBridge_+'"]').trigger(b,c)};a.pkp.classes.Handler.prototype.replaceWith=function(b){this.unbindGlobalAll();this.getHtmlElement().replaceWith(b)};a.pkp.classes.Handler.prototype.replacePartialWith=function(b,c){a.pkp.classes.Handler.hasHandler(c)?
a.pkp.classes.Handler.getHandler(c).replaceWith(b):(this.unbindPartial(c),c.replaceWith(b))};a.pkp.classes.Handler.prototype.html=function(b){this.unbindGlobalChildren();this.getHtmlElement().html(b)};a.pkp.classes.Handler.prototype.unbindPartial=function(b){a("*",b).each(function(){if(a.pkp.classes.Handler.hasHandler(a(this))){var c=a.pkp.classes.Handler.getHandler(a(this));c.callbackWrapper(c.unbindGlobalAll())}})};a.pkp.classes.Handler.checkContext_=function(b){if(!(b instanceof a.pkp.classes.Handler))throw Error("Trying to call handler method in non-handler context!");
}})(jQuery);(function(a){a.pkp.classes.TinyMCEHelper=function(){throw Error("Trying to instantiate the TinyMCEHelper singleton!");};a.pkp.classes.TinyMCEHelper.prototype.getVariableMap=function(b){b=a(b).attr("data-variables");return void 0!==b?a.parseJSON(decodeURIComponent(b)):[]};a.pkp.classes.TinyMCEHelper.prototype.getVariableTypesMap=function(b){b=a(b).attr("data-variablesType");return void 0!==b?a.parseJSON(decodeURIComponent(b)):[]};a.pkp.classes.TinyMCEHelper.prototype.getVariableElement=function(b,
c,d){d=a.pkp.classes.TinyMCEHelper.prototype.getVariableTypesMap(d);return void 0!=d[b]&&(d=d[b],d==a.pkp.cons.INSERT_TAG_VARIABLE_TYPE_PLAIN_TEXT)?a("<div/>").append(a("<span/>").text(c)):a("<div/>").append(a("<span/>").addClass("pkpTag mceNonEditable").attr("data-symbolic",b).text(c))}})(jQuery);(function(a){a.pkp.classes.linkAction=a.pkp.classes.linkAction||{};a.pkp.classes.linkAction.LinkActionRequest=function(b,c){this.$linkActionElement=b;this.options=c;b.is("a")&&c.url&&b.attr("href",c.url)};a.pkp.classes.linkAction.LinkActionRequest.prototype.$linkActionElement=null;a.pkp.classes.linkAction.LinkActionRequest.prototype.options=null;a.pkp.classes.linkAction.LinkActionRequest.prototype.activate=function(b,c){this.getLinkActionElement().trigger("actionStart");return!1};a.pkp.classes.linkAction.LinkActionRequest.prototype.finish=
function(){this.options.finishCallback&&this.options.finishCallback();this.getLinkActionElement().trigger("actionStop");return!1};a.pkp.classes.linkAction.LinkActionRequest.prototype.getUrl=function(){return this.options.url?this.options.url:null};a.pkp.classes.linkAction.LinkActionRequest.prototype.getOptions=function(){return this.options};a.pkp.classes.linkAction.LinkActionRequest.prototype.getLinkActionElement=function(){return this.$linkActionElement};a.pkp.classes.linkAction.LinkActionRequest.prototype.shouldDebounce=
function(){return!0}})(jQuery);(function(a){a.pkp.classes.linkAction.RedirectRequest=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.linkAction.RedirectRequest,a.pkp.classes.linkAction.LinkActionRequest);a.pkp.classes.linkAction.RedirectRequest.prototype.activate=function(b,c){var d=this.getOptions();window.open(d.url,d.name,d.specs);return this.parent("activate",b,c)}})(jQuery);(function(a){a.pkp.classes.linkAction.OpenWindowRequest=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.linkAction.OpenWindowRequest,a.pkp.classes.linkAction.LinkActionRequest);a.pkp.classes.linkAction.OpenWindowRequest.prototype.activate=function(b,c){var d=this.getOptions();window.open(d.url);return this.parent("activate",b,c)};a.pkp.classes.linkAction.OpenWindowRequest.prototype.shouldDebounce=function(){return!1}})(jQuery);(function(a){a.pkp.classes.linkAction.PostAndRedirectRequest=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.linkAction.PostAndRedirectRequest,a.pkp.classes.linkAction.LinkActionRequest);a.pkp.classes.linkAction.PostAndRedirectRequest.prototype.postJsonData_=null;a.pkp.classes.linkAction.PostAndRedirectRequest.prototype.activate=function(b,c){b=this.parent("activate",b,c);c=this.getOptions();var d=a.pkp.classes.Helper.curry(this.handleResponse_,this);a.post(c.postUrl,d,
"json");return b};a.pkp.classes.linkAction.PostAndRedirectRequest.prototype.finishCallback_=function(){var b=this.getLinkActionElement();b=a.pkp.classes.Handler.getHandler(b);this.finish();b.handleJson(this.postJsonData_)};a.pkp.classes.linkAction.PostAndRedirectRequest.prototype.handleResponse_=function(b){var c=this.getOptions(),d=null,e=null;this.postJsonData_=b;d=setInterval(function(){0==a.active&&(clearInterval(d),window.location=c.url)},100);e=a.pkp.classes.Helper.curry(this.finishCallback_,
this);setTimeout(e,2E3)}})(jQuery);(function(a){a.pkp.classes.linkAction.NullAction=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.linkAction.NullAction,a.pkp.classes.linkAction.LinkActionRequest);a.pkp.classes.linkAction.NullAction.prototype.activate=function(b,c){return this.parent("activate",b,c)};a.pkp.classes.linkAction.NullAction.prototype.shouldDebounce=function(){return!1}})(jQuery);(function(a){a.pkp.classes.linkAction.EventAction=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.linkAction.EventAction,a.pkp.classes.linkAction.LinkActionRequest);a.pkp.classes.linkAction.EventAction.prototype.activate=function(b,c){a(this.options.target).trigger(this.options.event,this.options);return this.parent("activate",b,c)};a.pkp.classes.linkAction.EventAction.prototype.shouldDebounce=function(){return!1}})(jQuery);(function(a){a.pkp.classes.linkAction.AjaxRequest=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.linkAction.AjaxRequest,a.pkp.classes.linkAction.LinkActionRequest);a.pkp.classes.linkAction.AjaxRequest.prototype.activate=function(b,c){b=this.parent("activate",b,c);c=this.getOptions();var d=a.pkp.classes.Helper.curry(this.handleResponse,this);switch(c.requestType){case "get":a.getJSON(c.url,c.data,d);break;case "post":a.post(c.url,c.data,d,"json")}return b};a.pkp.classes.linkAction.AjaxRequest.prototype.handleResponse=
function(b){this.getLinkActionElement().data("pkp.handler").handleJson(b);this.finish()}})(jQuery);(function(a){a.pkp.classes.linkAction.ModalRequest=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.linkAction.ModalRequest,a.pkp.classes.linkAction.LinkActionRequest);a.pkp.classes.linkAction.ModalRequest.prototype.$modal_=null;a.pkp.classes.linkAction.ModalRequest.prototype.activate=function(b,c){var d=this.getOptions(),e=this.getLinkActionElement(),f=e.text();void 0===d.title&&(""===f&&(f=e.attr("title")),d.title=f);e=a.pkp.classes.Helper.uuid();if(!d.modalHandler)throw Error('The "modalHandler" setting is required in a ModalRequest');
f=this.getLinkActionElement();f=a.pkp.classes.Handler.getHandler(f);f=a.extend(!0,{eventBridge:f.getStaticId()},d);this.$modal_=a('<div id="'+e+'" class="pkp_modal pkpModalWrapper" tabindex="-1"></div>').pkpHandler(d.modalHandler,f);a.pkp.classes.Handler.getHandler(this.$modal_).bind("pkpRemoveHandler",a.pkp.classes.Helper.curry(this.finish,this));return this.parent("activate",b,c)};a.pkp.classes.linkAction.ModalRequest.prototype.finish=function(){0!==this.$linkActionElement.attr("id").indexOf("newRoundTabContainer")&&
this.$linkActionElement.focus();this.$modal_.remove();return this.parent("finish")}})(jQuery);(function(a){a.pkp.classes.notification=a.pkp.classes.notification||{};a.pkp.classes.notification.NotificationHelper=function(){};a.pkp.classes.notification.NotificationHelper.redirectNotifyUserEvent=function(b,c){var d,e;if(void 0!==c&&void 0!==c.content)b.getHtmlElement().parent().trigger("notifyUser",[c]);else{var f=b.getHtmlElement();var k=!1;!(b instanceof a.pkp.controllers.SiteHandler)&&void 0!==c&&0<a(c).parents(".pkp_controllers_grid").length&&(f.parent().trigger("notifyUser"),k=!0);f=a(".pkp_notification",
f);var g=[];var n=0;for(d=f.length;n<d;n++){var v=a(f[n]);if(!(0<v.parents(":hidden").length)){var t=v.parents();var q=0;for(e=t.length;q<e;q++){var A=a(t[q]).data("pkp.handler");if(A instanceof a.pkp.classes.Handler){var B=a(t[q]);break}}if(void 0!==c&&(B.has(c[0]).length||B[0]===c[0])){if(0<v.parents(".ui-accordion:first").length&&(t=v.parents(".ui-accordion:first"),!t.has(c[0])))continue;g.push(v)}}}if(g.length)for(n=g.length-1;-1<n;n--)g[n].triggerHandler("notifyUser");else k||b.getHtmlElement().parent().trigger("notifyUser")}}})(jQuery);(function(a){a.pkp.classes.features=a.pkp.classes.features||{};a.pkp.classes.features.Feature=function(b,c){this.gridHandler=b;this.options_=c;this.addFeatureHtml(this.getGridHtmlElement(),c)};a.pkp.classes.features.Feature.prototype.gridHandler=null;a.pkp.classes.features.Feature.prototype.options_=null;a.pkp.classes.features.Feature.prototype.setOptions=function(b){this.options_=b};a.pkp.classes.features.Feature.prototype.getOptions=function(){return this.options_};a.pkp.classes.features.Feature.prototype.init=
function(){throw Error("Abstract method!");};a.pkp.classes.features.Feature.prototype.addElement=function(b){return!1};a.pkp.classes.features.Feature.prototype.replaceElement=function(b){return!1};a.pkp.classes.features.Feature.prototype.resequenceRows=function(b){return!1};a.pkp.classes.features.Feature.prototype.refreshGrid=function(b){return!1};a.pkp.classes.features.Feature.prototype.replaceElementResponseHandler=function(b){return!1};a.pkp.classes.features.Feature.prototype.callbackWrapper=function(b,
c){return this.gridHandler.callbackWrapper(b,c)};a.pkp.classes.features.Feature.prototype.addFeatureHtml=function(b,c){};a.pkp.classes.features.Feature.prototype.getGridHtmlElement=function(){return this.gridHandler.getHtmlElement()}})(jQuery);(function(a){a.pkp.classes.features.OrderItemsFeature=function(b,c){this.parent(b,c);this.$orderButton_=a(".pkp_linkaction_orderItems",this.getGridHtmlElement());this.$finishControl_=a(".order_finish_controls",this.getGridHtmlElement());0===this.$orderButton_.length&&(this.isOrdering=!0);this.itemsOrder=[]};a.pkp.classes.Helper.inherits(a.pkp.classes.features.OrderItemsFeature,a.pkp.classes.features.Feature);a.pkp.classes.features.OrderItemsFeature.prototype.itemsOrder=null;a.pkp.classes.features.OrderItemsFeature.prototype.isOrdering=
!1;a.pkp.classes.features.OrderItemsFeature.prototype.$orderButton_=null;a.pkp.classes.features.OrderItemsFeature.prototype.$cancelButton_=null;a.pkp.classes.features.OrderItemsFeature.prototype.$saveButton_=null;a.pkp.classes.features.OrderItemsFeature.prototype.$finishControl_=null;a.pkp.classes.features.OrderItemsFeature.prototype.getOrderButton=function(){return this.$orderButton_};a.pkp.classes.features.OrderItemsFeature.prototype.getFinishControl=function(){return this.$finishControl_};a.pkp.classes.features.OrderItemsFeature.prototype.getSaveOrderButton=
function(){return this.getFinishControl().find(".saveButton")};a.pkp.classes.features.OrderItemsFeature.prototype.getCancelOrderButton=function(){return this.getFinishControl().find(".cancelFormButton")};a.pkp.classes.features.OrderItemsFeature.prototype.getMoveItemRowActionSelector=function(){return".orderable .pkp_linkaction_moveItem"};a.pkp.classes.features.OrderItemsFeature.prototype.getMoveItemClasses=function(){return"pkp_helpers_moveicon ordering"};a.pkp.classes.features.OrderItemsFeature.prototype.dragStartCallback=
function(b,c,d){};a.pkp.classes.features.OrderItemsFeature.prototype.dragStopCallback=function(b,c,d){};a.pkp.classes.features.OrderItemsFeature.prototype.updateOrderCallback=function(b,c,d){};a.pkp.classes.features.OrderItemsFeature.prototype.init=function(){this.addOrderingClassToRows();this.toggleMoveItemRowAction(this.isOrdering);this.getGridHtmlElement().find("div.order_message").hide();this.toggleOrderLink_();this.isOrdering&&this.setupSortablePlugin()};a.pkp.classes.features.OrderItemsFeature.prototype.addFeatureHtml=
function(b,c){if(void 0!==c.orderFinishControls){var d=a(c.orderFinishControls);b.find("table").last().after(d);d.hide()}void 0!==c.orderMessage&&(c=c.orderMessage,b=b.find(".gridRow").filter(function(e,f){return!a(this).find("a.pkp_linkaction_moveItem").length}),b.find("td:first-child").prepend(c));this.updateOrderLinkVisibility_()};a.pkp.classes.features.OrderItemsFeature.prototype.addOrderingClassToRows=function(){this.gridHandler.getRows().filter(function(b,c){return a(this).find("a.pkp_linkaction_moveItem").length}).addClass("orderable")};
a.pkp.classes.features.OrderItemsFeature.prototype.setupSortablePlugin=function(){};a.pkp.classes.features.OrderItemsFeature.prototype.storeRowOrder=function(b,c){};a.pkp.classes.features.OrderItemsFeature.prototype.clickOrderHandler=function(){this.gridHandler.hideAllVisibleRowActions();this.storeOrder(this.gridHandler.getRows());this.toggleState(!0);return!1};a.pkp.classes.features.OrderItemsFeature.prototype.saveOrderHandler=function(){this.gridHandler.updateControlRowsPosition();this.unbindOrderFinishControlsHandlers_();
var b=this.gridHandler.getRows();this.storeOrder(b);return!1};a.pkp.classes.features.OrderItemsFeature.prototype.cancelOrderHandler=function(){this.gridHandler.resequenceRows(this.itemsOrder);this.toggleState(!1);return!1};a.pkp.classes.features.OrderItemsFeature.prototype.toggleState=function(b){this.isOrdering=b;this.toggleGridLinkActions_();this.toggleOrderLink_();this.toggleFinishControl_();this.toggleItemsDragMode();this.setupSortablePlugin();this.setupNonOrderableMessage_()};a.pkp.classes.features.OrderItemsFeature.prototype.storeOrder=
function(b){var c;this.itemsOrder=[];var d=0;for(c=b.length;d<c;d++){var e=a(b[d]);var f=e.attr("id");this.itemsOrder.push(f);this.storeRowOrder(d,e)}};a.pkp.classes.features.OrderItemsFeature.prototype.toggleItemsDragMode=function(){var b=this.isOrdering,c=this.gridHandler.getRows().filter(".orderable"),d=this.getMoveItemClasses();b?c.addClass(d):c.removeClass(d);this.toggleMoveItemRowAction(b)};a.pkp.classes.features.OrderItemsFeature.prototype.applySortPlgOnElements=function(b,c,d){var e=this.isOrdering,
f=this.gridHandler.callbackWrapper(this.dragStartCallback,this),k=this.gridHandler.callbackWrapper(this.dragStopCallback,this),g=this.gridHandler.callbackWrapper(this.updateOrderCallback,this);c={disabled:!e,items:c,activate:f,deactivate:k,update:g,tolerance:"pointer"};"object"===typeof d&&(c=a.extend(!0,c,d));b.sortable(c)};a.pkp.classes.features.OrderItemsFeature.prototype.getRowsDataId=function(b){var c,d=[];for(c in this.itemsOrder){var e=a("#"+this.itemsOrder[c],b);1>e.length||(e=this.gridHandler.getRowDataId(e),
d.push(e))}return d};a.pkp.classes.features.OrderItemsFeature.prototype.toggleMoveItemRowAction=function(b){var c=this.getGridHtmlElement(),d=a("div.row_actions",c),e="a:not("+this.getMoveItemRowActionSelector()+")",f=d.find(e);d=a(this.getMoveItemRowActionSelector(),c);b?(f.addClass("pkp_helpers_display_none"),d.show(),this.gridHandler.showRowActionsDiv()):(f.removeClass("pkp_helpers_display_none"),b=a(".gridRow div.row_actions",c),e=b.find(e),0===e.length&&this.gridHandler.hideRowActionsDiv(),d.hide())};
a.pkp.classes.features.OrderItemsFeature.prototype.addElement=function(b){this.addOrderingClassToRows();this.toggleItemsDragMode();return!1};a.pkp.classes.features.OrderItemsFeature.prototype.replaceElement=function(b){this.addOrderingClassToRows();this.toggleItemsDragMode();return!1};a.pkp.classes.features.OrderItemsFeature.prototype.replaceElementResponseHandler=function(b){this.updateOrderLinkVisibility_();this.setupNonOrderableMessage_();return!1};a.pkp.classes.features.OrderItemsFeature.prototype.updateOrderLinkVisibility_=
function(){var b=a(".pkp_linkaction_orderItems",this.getGridHtmlElement());1>=this.gridHandler.getRows().length?b.hide():b.show()};a.pkp.classes.features.OrderItemsFeature.prototype.toggleGridLinkActions_=function(){var b=this.isOrdering,c=a(".pkp_controllers_linkAction",this.getGridHtmlElement()).not(this.getMoveItemRowActionSelector()).not(this.getOrderButton()).not(this.getFinishControl().find("*"));this.gridHandler.changeLinkActionsState(!b,c)};a.pkp.classes.features.OrderItemsFeature.prototype.toggleOrderLink_=
function(){if(this.isOrdering)this.$orderButton_.unbind("click"),this.$orderButton_.attr("disabled","disabled");else{var b=this.gridHandler.callbackWrapper(this.clickOrderHandler,this);this.$orderButton_.click(b);this.$orderButton_.removeAttr("disabled")}};a.pkp.classes.features.OrderItemsFeature.prototype.toggleFinishControl_=function(){this.isOrdering?(this.bindOrderFinishControlsHandlers_(),this.getFinishControl().slideDown(300)):(this.unbindOrderFinishControlsHandlers_(),this.getFinishControl().slideUp(300))};
a.pkp.classes.features.OrderItemsFeature.prototype.bindOrderFinishControlsHandlers_=function(){var b=this.getSaveOrderButton(),c=this.getCancelOrderButton(),d=this.gridHandler.callbackWrapper(this.cancelOrderHandler,this),e=this.gridHandler.callbackWrapper(this.saveOrderHandler,this);b.click(e);c.click(d)};a.pkp.classes.features.OrderItemsFeature.prototype.unbindOrderFinishControlsHandlers_=function(){this.getSaveOrderButton().unbind("click");this.getCancelOrderButton().unbind("click")};a.pkp.classes.features.OrderItemsFeature.prototype.setupNonOrderableMessage_=
function(){this.isOrdering?this.gridHandler.getRows().hover(function(){a(this).find("div.order_message").toggle()}):this.gridHandler.getRows().unbind("mouseenter mouseleave")}})(jQuery);(function(a){a.pkp.classes.features.OrderGridItemsFeature=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.features.OrderGridItemsFeature,a.pkp.classes.features.OrderItemsFeature);a.pkp.classes.features.OrderGridItemsFeature.prototype.setupSortablePlugin=function(){this.applySortPlgOnElements(this.getGridHtmlElement(),"tr.orderable",null)};a.pkp.classes.features.OrderGridItemsFeature.prototype.saveOrderHandler=function(){var b=this.getOptions();this.parent("saveOrderHandler");
var c=JSON.stringify(this.getItemsDataId());var d=this.callbackWrapper(this.saveOrderResponseHandler_,this);a.post(b.saveItemsSequenceUrl,{data:c,csrfToken:b.csrfToken},d,"json");return!1};a.pkp.classes.features.OrderGridItemsFeature.prototype.getItemsDataId=function(){return this.getRowsDataId(this.getGridHtmlElement())};a.pkp.classes.features.OrderGridItemsFeature.prototype.saveOrderResponseHandler_=function(b,c){this.gridHandler.handleJson(c);this.toggleState(!1)}})(jQuery);(function(a){a.pkp.classes.features.OrderCategoryGridItemsFeature=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.features.OrderCategoryGridItemsFeature,a.pkp.classes.features.OrderGridItemsFeature);a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.setupSortablePlugin=function(){var b;this.applySortPlgOnElements(this.getGridHtmlElement(),"tbody.orderable",null);var c=navigator.userAgent.toLowerCase();if(!(/msie/.test(c)&&8>=parseInt(c.substr(c.indexOf("msie")+
5,1),10))){c=this.gridHandler.getCategories();var d=0;for(b=c.length;d<b;d++){var e=a(c[d]);this.applySortPlgOnElements(e,"tr.orderable",null)}}};a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.saveOrderHandler=function(){this.gridHandler.updateEmptyPlaceholderPosition();this.parent("saveOrderHandler");return!1};a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.cancelOrderHandler=function(){var b=this.getCategorySequence_(this.itemsOrder);this.parent("cancelOrderHandler");
this.gridHandler.resequenceCategories(b);return!1};a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.toggleItemsDragMode=function(){this.parent("toggleItemsDragMode");var b=this.gridHandler.getCategories(),c;var d=0;for(c=b.length;d<c;d++){var e=a(b[d]);this.toggleCategoryDragMode_(e)}};a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.addOrderingClassToRows=function(){var b=this.getOptions();b=parseInt(b.type,10);if(b==a.pkp.cons.ORDER_CATEGORY_GRID_CATEGORIES_ONLY||b==
a.pkp.cons.ORDER_CATEGORY_GRID_CATEGORIES_AND_ROWS){var c=this.gridHandler.getCategories();c.addClass("orderable")}b!=a.pkp.cons.ORDER_CATEGORY_GRID_CATEGORIES_ROWS_ONLY&&b!=a.pkp.cons.ORDER_CATEGORY_GRID_CATEGORIES_AND_ROWS||this.parent("addOrderingClassToRows");this.gridHandler.getCategoryRow().removeClass("orderable")};a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.getItemsDataId=function(){var b=this.getCategorySequence_(this.itemsOrder),c=[],d;var e=0;for(d=b.length;e<d;e++){var f=
a("#"+b[e]);var k=this.getRowsDataId(f);f=this.gridHandler.getCategoryDataId(f);c.push({categoryId:f,rowsId:k})}return c};a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.toggleCategoryDragMode_=function(b){var c=this.isOrdering;b=this.gridHandler.getCategoryRow(b);b=a("td:first",b);var d=this.getMoveItemClasses();c?b.addClass(d):b.removeClass(d)};a.pkp.classes.features.OrderCategoryGridItemsFeature.prototype.getCategorySequence_=function(b){var c,d=[];var e=0;for(c=b.length;e<c;e++){var f=
this.gridHandler.getCategoryDataIdByRowId(b[e]);f=this.gridHandler.getCategoryIdPrefix()+f;-1<a.inArray(f,d)||d.push(f)}return d}})(jQuery);(function(a){a.pkp.classes.features.GeneralPagingFeature=function(b,c){c.defaultItemsPerPage=parseInt(c.defaultItemsPerPage,10);c.currentItemsPerPage=parseInt(c.currentItemsPerPage,10);c.itemsTotal=c.itemsTotal?parseInt(c.itemsTotal,10):0;c.currentPage=parseInt(c.currentPage,10);this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.features.GeneralPagingFeature,a.pkp.classes.features.Feature);a.pkp.classes.features.GeneralPagingFeature.prototype.getOptions=function(){return this.parent("getOptions")};
a.pkp.classes.features.GeneralPagingFeature.prototype.setGridParams=function(b){var c=this.getOptions();c.hasOwnProperty("filter")&&(c=a.parseJSON(c.filter),a.extend(!0,b,c));this.gridHandler.setFetchExtraParams(b)}})(jQuery);(function(a){a.pkp.classes.features.PagingFeature=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.features.PagingFeature,a.pkp.classes.features.GeneralPagingFeature);a.pkp.classes.features.PagingFeature.prototype.init=function(){this.configPagingLinks_();this.configItemsPerPageElement_()};a.pkp.classes.features.PagingFeature.prototype.addFeatureHtml=function(b,c){b.append(c.pagingMarkup)};a.pkp.classes.features.PagingFeature.prototype.resequenceRows=function(b){b=this.gridHandler.getRows();
var c;var d=this.getOptions();d=b.length-d.currentItemsPerPage;if(0<d)for(c=0;c<d;c++)this.gridHandler.deleteElement(b.first(),!0);return!1};a.pkp.classes.features.PagingFeature.prototype.refreshGrid=function(b){var c=this.getOptions();b=this.gridHandler.getFetchExtraParams();b[c.pageParamName]=c.currentPage;b[c.itemsPerPageParamName]=c.currentItemsPerPage;c=this.gridHandler.getRows().first();var d=this.gridHandler.getRows().last();b.topLimitRowId=0==c.length?0:this.gridHandler.getRowDataId(c);b.bottomLimitRowId=
0==d.length?0:this.gridHandler.getRowDataId(d);this.setGridParams(b);return!1};a.pkp.classes.features.PagingFeature.prototype.replaceElementResponseHandler=function(b){var c=this.getOptions();if(void 0!=b.deletedRowReplacement){var d=b.deletedRowReplacement;this.gridHandler.insertOrReplaceElement(d)}void 0!=b.pagingInfo&&(d=b.pagingInfo,this.setOptions(d),this.gridHandler.replacePartialWith(d.pagingMarkup,a("div.gridPaging",this.getGridHtmlElement())),this.init());b.loadLastPage&&this.getGridHtmlElement().trigger("dataChanged");
void 0!=b.newTopRow&&(d=this.gridHandler.getRows(),c.currentItemsPerPage==d.length&&this.gridHandler.deleteElement(d.last(),!0),d=b.newTopRow,this.gridHandler.insertOrReplaceElement(d,!0));return!1};a.pkp.classes.features.PagingFeature.prototype.configPagingLinks_=function(){var b,c,d,e;var f=this.getOptions();if(b=a("div.gridPaging",this.getGridHtmlElement())){var k=this.callbackWrapper(function(v,t){d=new RegExp("[?&]"+f.pageParamName+"(?:=([^&]*))?","i");e=d.exec(a(t.target).attr("href"));null!=
e&&(f.currentPage=parseInt(e[1],10),this.getGridHtmlElement().trigger("dataChanged"));return!1},this);b=b.find("a").not(".showMoreItems").not(".showLessItems");var g=0;for(c=b.length;g<c;g++){var n=a(b[g]);n.click(k)}}};a.pkp.classes.features.PagingFeature.prototype.configItemsPerPageElement_=function(){var b;var c=this.getOptions();if(b=a("div.gridPaging",this.getGridHtmlElement())){var d=this.callbackWrapper(function(g,n){c.currentItemsPerPage=parseInt(a("option",n.target).filter(":selected").attr("value"),
10);c.currentPage=1;this.getGridHtmlElement().trigger("dataChanged");return!1},this);var e=b.find("select.itemsPerPage");var f=[10,25,50,75,100];0>a.inArray(c.defaultItemsPerPage,f)&&f.push(c.defaultItemsPerPage);f.sort(function(g,n){return g-n});if(c.itemsTotal<=f[0])a("div.gridItemsPerPage",b).hide();else{var k=f.length-1;for(b=0;b<=k;b++)e.append(a('<option value="'+f[b]+'">'+f[b]+"</option>"));e.val(c.currentItemsPerPage.toString());e.change(d)}}}})(jQuery);(function(a){a.pkp.classes.features.InfiniteScrollingFeature=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.features.InfiniteScrollingFeature,a.pkp.classes.features.GeneralPagingFeature);a.pkp.classes.features.InfiniteScrollingFeature.prototype.$scrollableElement_=a();a.pkp.classes.features.InfiniteScrollingFeature.prototype.observeScrollCallback_=function(){};a.pkp.classes.features.InfiniteScrollingFeature.prototype.init=function(){var b=a("div.scrollable",this.getGridHtmlElement());
b.length||(this.gridHandler.publishEvent("pkpObserveScrolling"),this.gridHandler.publishEvent("pkpRemoveScrollingObserver"));this.$scrollableElement_=b;this.observeScrollCallback_=this.gridHandler.callbackWrapper(this.observeScroll_,this);this.addScrollHandler_();this.fixGridHeight_();this.addPagingDataToRows_()};a.pkp.classes.features.InfiniteScrollingFeature.prototype.addFeatureHtml=function(b,c){b.append(c.pagingMarkup);b.find(".pkp_linkaction_moreItems").click(this.gridHandler.callbackWrapper(this.loadMoreItems_,
this))};a.pkp.classes.features.InfiniteScrollingFeature.prototype.refreshGrid=function(b){var c=this.getOptions();var d=this.gridHandler.getFetchExtraParams();d[c.pageParamName]=c.currentPage;b&&b!==a.pkp.controllers.grid.GridHandler.FETCH_ALL_ROWS_ID&&(b=this.gridHandler.getRowByDataId(b),1==b.length&&(d[c.pageParamName]=Number(b.attr("data-paging"))));d[c.itemsPerPageParamName]=c.currentItemsPerPage;this.setGridParams(d);return!1};a.pkp.classes.features.InfiniteScrollingFeature.prototype.replaceElementResponseHandler=
function(b){this.getOptions();if(void 0!=b.deletedRowReplacement){var c=b.deletedRowReplacement;this.gridHandler.insertOrReplaceElement(c);this.updatePagingDataInAllRows_()}this.addScrollHandler_();void 0!=b.pagingInfo&&(b=b.pagingInfo,this.setOptions(b),void 0!=b.pagingMarkup&&a("div.gridPagingScrolling",this.getGridHtmlElement()).replaceWith(b.pagingMarkup));this.addPagingDataToRows_();this.toggleLoadingContainer_();this.getGridHtmlElement().find(".pkp_linkaction_moreItems").click(this.gridHandler.callbackWrapper(this.loadMoreItems_,
this));return!1};a.pkp.classes.features.InfiniteScrollingFeature.prototype.observeScroll_=function(b,c){if(this.getOptions().itemsTotal==this.gridHandler.getRows().length||!this.getGridHtmlElement().is(":visible"))return!1;if(a(b).hasClass("scrollable")){c=a(b).height();var d=b.scrollHeight}else c=a.pkp.controllers.SiteHandler.prototype.getWindowDimensions(),c=c.height,d=this.getGridHtmlElement().offset().top+this.getGridHtmlElement().height();c+a(b).scrollTop()>=d&&(this.$scrollableElement_.length?
this.$scrollableElement_.unbind("scroll"):this.getGridHtmlElement().trigger("pkpRemoveScrollingObserver",[this.observeScrollCallback_]),this.loadMoreItems_());return!1};a.pkp.classes.features.InfiniteScrollingFeature.prototype.fixGridHeight_=function(){var b=a("div.scrollable",this.getGridHtmlElement()),c,d,e,f,k;0<b.length&&(f=setInterval(function(){if(b.is(":visible"))for(clearInterval(f),k=b.length,c=0,d=k;c<d;c++)e=a(b[c]),e.get(0).scrollHeight>e.height()&&e.css("max-height",e.get(0).scrollHeight-
10)},300))};a.pkp.classes.features.InfiniteScrollingFeature.prototype.addPagingDataToRows_=function(){var b=this.getOptions();this.gridHandler.getRows().filter("tr:not([data-paging])").attr("data-paging",b.currentPage)};a.pkp.classes.features.InfiniteScrollingFeature.prototype.updatePagingDataInAllRows_=function(){var b=this.getOptions(),c,d=1,e=1;var f=this.gridHandler.getRows();f.removeAttr("data-paging");var k=0;for(c=f.length;k<c;k++)a(f[k]).attr("data-paging",d),e++,e>b.currentItemsPerPage&&
(e=1,d++)};a.pkp.classes.features.InfiniteScrollingFeature.prototype.addScrollHandler_=function(){var b=this.$scrollableElement_;b.length?b.scroll(this.observeScrollCallback_):this.getGridHtmlElement().trigger("pkpObserveScrolling",[this.observeScrollCallback_])};a.pkp.classes.features.InfiniteScrollingFeature.prototype.toggleLoadingContainer_=function(b){var c=this.getGridHtmlElement().find("div.gridPagingScrolling div.pkp_loading"),d=this.$scrollableElement_;c=c.height();b?(this.getGridHtmlElement().addClass("loading"),
b=d.scrollTop(),d.scrollTop(b+c)):this.getGridHtmlElement().removeClass("loading")};a.pkp.classes.features.InfiniteScrollingFeature.prototype.loadMoreItems_=function(){var b=this.getOptions();this.toggleLoadingContainer_(!0);b.currentPage=Number(a("tr.gridRow",this.getGridHtmlElement()).last().attr("data-paging"))+1;this.getGridHtmlElement().trigger("dataChanged",[a.pkp.controllers.grid.GridHandler.FETCH_ALL_ROWS_ID])}})(jQuery);(function(a){a.pkp.classes.features.CollapsibleGridFeature=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.classes.features.CollapsibleGridFeature,a.pkp.classes.features.Feature);a.pkp.classes.features.CollapsibleGridFeature.prototype.getControlSelector=function(){return"a[id^='collapsibleGridControl-expandGridControlLink-button-']"};a.pkp.classes.features.CollapsibleGridFeature.prototype.init=function(){a(this.getControlSelector(),this.getGridHtmlElement()).click(this.callbackWrapper(this.toggleGridClickHandler_,
this))};a.pkp.classes.features.CollapsibleGridFeature.prototype.addFeatureHtml=function(b,c){b.find("div.grid_header_bar").prepend(c.collapsibleLink)};a.pkp.classes.features.CollapsibleGridFeature.prototype.toggleGridClickHandler_=function(b,c){b=this.getGridHtmlElement().find(this.getControlSelector());this.getGridHtmlElement().find("div.grid_header").siblings().toggle();b.toggleClass("expand_all").toggleClass("collapse_all");this.getGridHtmlElement().find("div.grid_header_bar .search_extras_collapse").click();
this.getGridHtmlElement().find("div.grid_header span.options").toggle();return!1}})(jQuery);(function(a){a.pkp.controllers.SiteHandler=function(b,c){this.parent(b,c);this.options_=c;this.unsavedFormElements_=[];a(".go").button();this.bind("redirectRequested",this.redirectToUrl);this.bind("notifyUser",this.fetchNotificationHandler_);this.bind("reloadTab",this.reloadTabHandler_);this.bind("callWhenClickOutside",this.callWhenClickOutsideHandler_);this.bind("mousedown",this.mouseDownHandler_);a(window).bind("beforeunload",this.pageUnloadHandler_);a.ajaxSetup({cache:!1});c.hasSystemNotifications&&
this.trigger("notifyUser");this.bind("formChanged",this.callbackWrapper(this.registerUnsavedFormElement_));this.bind("unregisterChangedForm",this.callbackWrapper(this.unregisterUnsavedFormElement_));this.bind("unregisterAllForms",this.callbackWrapper(this.unregisterAllFormElements_));this.bind("pkpModalOpen",this.callbackWrapper(this.openModal_));this.bind("pkpModalClose",this.callbackWrapper(this.closeModal_));this.bind("pkpObserveScrolling",this.callbackWrapper(this.registerScrollingObserver_));
this.bind("pkpRemoveScrollingObserver",this.callbackWrapper(this.unregisterScrollingObserver_));this.outsideClickChecks_={};this.initializeTinyMCE()};a.pkp.classes.Helper.inherits(a.pkp.controllers.SiteHandler,a.pkp.classes.Handler);a.pkp.controllers.SiteHandler.prototype.helpContext_=null;a.pkp.controllers.SiteHandler.prototype.options_=null;a.pkp.controllers.SiteHandler.prototype.outsideClickChecks_=null;a.pkp.controllers.SiteHandler.prototype.unsavedFormElements_=null;a.pkp.controllers.SiteHandler.prototype.tinymceParams_=
null;a.pkp.controllers.SiteHandler.prototype.initializeTinyMCE=function(){if("undefined"!==typeof tinyMCE){tinyMCE.PluginManager.load("pkpTags",a.pkp.app.baseUrl+"/plugins/generic/tinymce/plugins/pkpTags/plugin.js");tinyMCE.PluginManager.load("pkpwordcount",a.pkp.app.baseUrl+"/plugins/generic/tinymce/plugins/pkpWordcount/plugin.js");var b={width:"100%",resize:"both",entity_encoding:"raw",plugins:"fullscreen link lists code image -pkpTags".split(" "),convert_urls:!1,forced_root_block:"p",paste_auto_cleanup_on_paste:!0,
apply_source_formatting:!1,toolbar:"copy paste | bold italic underline | link unlink code fullscreen | image | pkpTags",richToolbar:"copy paste | bold italic underline | bullist numlist | superscript subscript | link unlink code fullscreen | image | pkpTags",statusbar:!1,content_css:a.pkp.app.tinyMceContentCSS,browser_spellcheck:!0,skin:"tinymce-5",license_key:"gpl",menubar:!1};"undefined"!==typeof a.pkp.plugins.generic.tinymceplugin&&"undefined"!==typeof a.pkp.plugins.generic.tinymceplugin.uploadUrl&&
(b.paste_data_images=!0,b.relative_urls=!1,b.remove_script_host=!1,b.images_upload_handler=function(c,d){return new Promise(function(e,f){var k=new FormData;k.append("file",c.blob(),c.filename());a.ajax({method:"POST",url:a.pkp.plugins.generic.tinymceplugin.uploadUrl,data:k,processData:!1,contentType:!1,headers:{"X-Csrf-Token":pkp.currentUser.csrfToken},success:function(g){e(g.url)},error:function(g){f(g.responseJSON.error)}})})});b="undefined"!==typeof a.pkp.plugins.generic.tinymceplugin&&typeof a.pkp.plugins.generic.tinymceplugin.tinymceParams?
a.extend({},b,a.pkp.plugins.generic.tinymceplugin.tinymceParams):a.extend({},b);b.init_instance_callback=a.pkp.controllers.SiteHandler.prototype.triggerTinyMCEInitialized;b.setup=a.pkp.controllers.SiteHandler.prototype.triggerTinyMCESetup;a.pkp.controllers.SiteHandler.prototype.tinymceParams_=b;tinyMCE.init(b)}};a.pkp.controllers.SiteHandler.prototype.triggerTinyMCEInitialized=function(b){a("#"+a.pkp.classes.Helper.escapeJQuerySelector(b.id)).trigger("tinyMCEInitialized",[b])};a.pkp.controllers.SiteHandler.prototype.triggerTinyMCESetup=
function(b){var c=a("#"+a.pkp.classes.Helper.escapeJQuerySelector(b.id));c.attr("readonly")&&(a.pkp.controllers.SiteHandler.prototype.tinymceParams_.readonly=!0);c.attr("wordCount")&&0<c.attr("wordCount")&&(a.pkp.controllers.SiteHandler.prototype.tinymceParams_.plugins.push("pkpwordcount"),a.pkp.controllers.SiteHandler.prototype.tinymceParams_.statusbar=!0);c.attr("dir")&&(a.pkp.controllers.SiteHandler.prototype.tinymceParams_.directionality=c.attr("dir"));c=c.attr("rows")||10;a.pkp.controllers.SiteHandler.prototype.tinymceParams_.height=
(20*c).toString()+"px";b.on("init",function(d){var e=a("#"+d.id);var f=a("#"+d.id).attr("placeholder");if(""!==f){var k=a("<span></span>");k.html(f);k.addClass("mcePlaceholder");k.attr("id","mcePlaceholder-"+d.id);d.target.getContent().length&&k.hide();d=a("<div></div>");d.addClass("mcePlaceholderParent");e.wrap(d);e.parent().append(k)}});b.on("activate",function(d){a("#mcePlaceholder-"+d.id).hide()});b.on("deactivate",function(d){d.target.getContent().length||a("#mcePlaceholder-"+d.id).show()});
b.on("BeforeSetContent",function(d){var e=a.pkp.classes.TinyMCEHelper.prototype.getVariableMap("#"+a.pkp.classes.Helper.escapeJQuerySelector(b.id));d.content=d.content.replace(/\{\$([a-zA-Z]+)\}(?![^<]*>)/g,function(f,k,g,n){return void 0!==e[k]?a.pkp.classes.TinyMCEHelper.prototype.getVariableElement(k,e[k],"#"+b.id).html():f})});b.on("GetContent",function(d){var e=a("<div>"+d.content+"</div>");e.find(".pkpTag").replaceWith(function(){return"{$"+a(this).attr("data-symbolic")+"}"});d.content=e.html()});
b.on("FullscreenStateChanged init",function(d){d=d.target;var e=a(d.editorContainer);d.plugins.fullscreen&&(d.plugins.fullscreen.isFullscreen()?e.find(".tox-menubar").show():e.find(".tox-menubar").hide())})};a.pkp.controllers.SiteHandler.prototype.getWindowDimensions=function(){return{height:a(window).height(),width:a(window).width()}};a.pkp.controllers.SiteHandler.prototype.redirectToUrl=function(b,c,d){window.location=d};a.pkp.controllers.SiteHandler.prototype.registerUnsavedFormElement_=function(b,
c,d){b=a(d.target.lastElementChild).attr("id");-1==a.inArray(b,this.unsavedFormElements_)&&this.unsavedFormElements_.push(b)};a.pkp.controllers.SiteHandler.prototype.unregisterUnsavedFormElement_=function(b,c,d){b=a(d.target.lastElementChild).attr("id");b=a.inArray(b,this.unsavedFormElements_);-1!==b&&delete this.unsavedFormElements_[b]};a.pkp.controllers.SiteHandler.prototype.unregisterAllFormElements_=function(){this.unsavedFormElements_=[]};a.pkp.controllers.SiteHandler.prototype.fetchNotificationHandler_=
function(b,c,d){void 0!==d?this.showNotification_(d):a.ajax({url:this.options_.fetchNotificationUrl,data:this.options_.requestOptions,success:this.callbackWrapper(this.showNotificationResponseHandler_),dataType:"json",async:!1})};a.pkp.controllers.SiteHandler.prototype.reloadTabHandler_=function(b,c,d){a(d.tabsSelector).tabs("load",d.tabSelector)};a.pkp.controllers.SiteHandler.prototype.callWhenClickOutsideHandler_=function(b,c,d){void 0!==d.container&&(b=d.container.attr("id"),d.clear?delete this.outsideClickChecks_[b]:
void 0!==d.callback&&(this.outsideClickChecks_[b]=d))};a.pkp.controllers.SiteHandler.prototype.mouseDownHandler_=function(b,c){var d;if(!a.isEmptyObject(this.outsideClickChecks_))for(d in this.outsideClickChecks_)this.processOutsideClickCheck_(this.outsideClickChecks_[d],c);return!0};a.pkp.controllers.SiteHandler.prototype.processOutsideClickCheck_=function(b,c){if("click"!==c.type&&"mousedown"!==c.type&&"mouseup"!==c.type)throw Error("Can not check outside click with the passed event: "+c.type+".");
var d=b.container;return d.is(":hidden")?!1:0===d.has(c.target).length?(delete this.outsideClickChecks_[d.attr("id")],b.callback(),!0):!1};a.pkp.controllers.SiteHandler.prototype.pageUnloadHandler_=function(b,c){var d;b=a.pkp.classes.Handler.getHandler(a("body"));c=0;for(d in b.unsavedFormElements_)d&&c++;if(0<c)return pkp.localeKeys["form.dataHasChanged"]};a.pkp.controllers.SiteHandler.prototype.isFormUnsaved=function(b){return null!==this.unsavedFormElements_&&void 0!==this.unsavedFormElements_[b]?
!0:!1};a.pkp.controllers.SiteHandler.prototype.showNotificationResponseHandler_=function(b,c){this.showNotification_(c)};a.pkp.controllers.SiteHandler.prototype.showNotification_=function(b){var c,d;b=this.handleJson(b);if(!1!==b&&b.content.general)for(c in b=b.content.general,b)for(d in b[c]){var e=b[c][d].addclass;var f="notice";if("notifySuccess"==e)f="success";else if("notifyWarning"==e||"notifyError"==e||"notifyFormError"==e||"notifyForbidden"==e)f="warning";pkp.eventBus.$emit("notify",b[c][d].text,
f)}};a.pkp.controllers.SiteHandler.prototype.openModal_=function(b,c,d,e){this.getHtmlElement().addClass("modal_is_visible")};a.pkp.controllers.SiteHandler.prototype.closeModal_=function(b,c,d,e){b=this.getHtmlElement();b.find(".pkp_modal.is_visible").length||b.removeClass("modal_is_visible")};a.pkp.controllers.SiteHandler.prototype.registerScrollingObserver_=function(b,c,d,e){a(document).scroll(e);return!1};a.pkp.controllers.SiteHandler.prototype.unregisterScrollingObserver_=function(b,c,d,e){a(document).unbind("scroll",
e);return!1}})(jQuery);(function(a){a.pkp.controllers.UrlInDivHandler=function(b,c){this.parent(b,c);this.sourceUrl_=c.sourceUrl;this.reload();c.refreshOn&&this.bindGlobal(c.refreshOn,this.reload)};a.pkp.classes.Helper.inherits(a.pkp.controllers.UrlInDivHandler,a.pkp.classes.Handler);a.pkp.controllers.UrlInDivHandler.sourceUrl_=null;a.pkp.controllers.UrlInDivHandler.prototype.reload=function(){a.get(this.sourceUrl_,this.callbackWrapper(this.handleLoadedContent_),"json")};a.pkp.controllers.UrlInDivHandler.prototype.getSourceUrl=
function(){return this.sourceUrl_};a.pkp.controllers.UrlInDivHandler.prototype.setSourceUrl=function(b){this.sourceUrl_=b};a.pkp.controllers.UrlInDivHandler.prototype.handleLoadedContent_=function(b,c){b=this.handleJson(c);var d=this;!0===b.status?void 0===b.content?this.getHtmlElement().hide():(/msie/.test(navigator.userAgent.toLowerCase())?this.html(b.content):(this.getHtmlElement().hide(),this.html(b.content),this.getHtmlElement().fadeIn(400)),a(function(){d.trigger("urlInDivLoaded",[d.getHtmlElement().attr("id")])})):
alert(b.content);return!1}})(jQuery);(function(a){a.pkp.controllers.EditorialActionsHandler=function(b,c){this.parent(b,c);b.find(".pkp_workflow_change_decision").click(this.callbackWrapper(this.showActions_));b.find("[data-decision]").click(this.callbackWrapper(this.emitRevisionDecision_));b.find("[data-recommendation]").click(this.callbackWrapper(this.emitRevisionRecommendation_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.EditorialActionsHandler,a.pkp.classes.Handler);a.pkp.controllers.EditorialActionsHandler.prototype.showActions_=
function(b,c){this.getHtmlElement().find(".pkp_workflow_change_decision").hide();this.getHtmlElement().find(".pkp_workflow_decisions_options").removeClass("pkp_workflow_decisions_options_hidden")};a.pkp.controllers.EditorialActionsHandler.prototype.emitRevisionDecision_=function(b,c){b=a(b);pkp.eventBus.$emit("decision:revisions",b.data("reviewRoundId"))};a.pkp.controllers.EditorialActionsHandler.prototype.emitRevisionRecommendation_=function(b,c){b=a(b);pkp.eventBus.$emit("recommendation:revisions",
b.data("reviewRoundId"))}})(jQuery);(function(a){a.pkp.controllers.ExtrasOnDemandHandler=function(b,c){this.parent(b,c);a(".toggleExtras",b).click(this.callbackWrapper(this.toggleExtras))};a.pkp.classes.Helper.inherits(a.pkp.controllers.ExtrasOnDemandHandler,a.pkp.classes.Handler);a.pkp.controllers.ExtrasOnDemandHandler.prototype.toggleExtras=function(b,c){b=this.getHtmlElement();c.preventDefault();b.toggleClass("active");b.hasClass("active")&&(c=b.closest(".scrollable"),0<c.length&&this.scrollToMakeVisible_(b,c))};a.pkp.controllers.ExtrasOnDemandHandler.prototype.scrollToMakeVisible_=
function(b,c){var d=b.position().top;var e=c.position().top;var f=parseInt(c.scrollTop(),10);d>e?(b=Math.ceil(d+b.height()-c.height()),0<b&&c.scrollTop(f+b)):(f=Math.max(Math.floor(f+d-e),0),c.scrollTop(f))}})(jQuery);(function(a){a.pkp.controllers.TabHandler=function(b,c){var d,e=this;this.parent(b,c);this.bind("tabsbeforeactivate",this.tabsBeforeActivate);this.bind("tabsactivate",this.tabsActivate);this.bind("tabscreate",this.tabsCreate);this.bind("tabsbeforeload",this.tabsBeforeLoad);this.bind("tabsload",this.tabsLoad);this.bind("addTab",this.addTab);c.emptyLastTab&&(this.emptyLastTab_=c.emptyLastTab);var f=document.location.toString();if(f.match("#")){f=f.split("#")[1];var k=b.find("li a");for(d=0;d<k.length;d++)f==
k[d].getAttribute("name")&&(c.selected=d)}b.tabs({beforeLoad:function(g,n){n.ajaxSettings.dataType="json";n.jqXHR.setRequestHeader("Accept","application/json");n.ajaxSettings.dataFilter=e.callbackWrapper(e.dataFilter)},disabled:c.disabled,active:c.selected});window.addEventListener("hashchange",function(g){g=g.newURL.split("#");2>g.length||(g=b.find('li > a[name="'+g[1]+'"]'),g.length&&g.click())},!1)};a.pkp.classes.Helper.inherits(a.pkp.controllers.TabHandler,a.pkp.classes.Handler);a.pkp.controllers.TabHandler.prototype.$currentTab_=
null;a.pkp.controllers.TabHandler.prototype.currentTabIndex_=0;a.pkp.controllers.TabHandler.prototype.tabsBeforeActivate=function(b,c,d){var e=!1;this.$currentTab_.find("form").each(function(f){if(a.pkp.classes.Handler.hasHandler(a("#"+a(this).attr("id")))&&a.pkp.classes.Handler.getHandler(a("#"+a(this).attr("id"))).formChangesTracked)return e=!0,!1});this.$currentTab_.find(".hasDatepicker").datepicker("hide");if(e)if(confirm(pkp.localeKeys["form.dataHasChanged"]))this.trigger("unregisterAllForms");
else return!1;this.emptyLastTab_&&(a(window).one("error",function(f,k,g){return!1}),this.$currentTab_&&(this.unbindPartial(this.$currentTab_),this.$currentTab_.empty()));return!0};a.pkp.controllers.TabHandler.prototype.tabsCreate=function(b,c,d){this.currentTabIndex_=d.tab.index();this.$currentTab_=d.panel.jquery?d.panel:a(d.panel);return!0};a.pkp.controllers.TabHandler.prototype.tabsActivate=function(b,c,d){this.currentTabIndex_=d.newTab.index();this.$currentTab_=d.newPanel.jquery?d.newPanel:a(d.newPanel);
return!0};a.pkp.controllers.TabHandler.prototype.tabsLoad=function(b,c,d){return!0};a.pkp.controllers.TabHandler.prototype.tabsBeforeLoad=function(b,c,d){this.unbindPartial(a("#"+d.tab.attr("aria-controls")));d.ajaxSettings.cache=!1;d.ajaxSettings.dataFilter=this.callbackWrapper(this.dataFilter)};a.pkp.controllers.TabHandler.prototype.dataFilter=function(b,c){b=this.handleJson(a.parseJSON(c));return!1===b?"":JSON.stringify(b.content)};a.pkp.controllers.TabHandler.prototype.addTab=function(b,c,d){var e=
this.getHtmlElement();b=e.children("ul").children("li").length+1;c=a("<a/>").text(d.title).attr("href",d.url);d=a("<a/>").addClass("close").text(pkp.localeKeys["common.close"]).attr("href","#");c=a("<li/>").append(c).append(d);d.click(function(){var f=a(this).closest("li"),k=a("#"+f.attr("aria-controls"));var g=!1;k.find("form").each(function(){if(a.pkp.classes.Handler.getHandler(a(this)).formChangesTracked&&!confirm(pkp.localeKeys["form.dataHasChanged"]))return g=!0,!1});if(!g){k.find("form").each(function(){var v=
a.pkp.classes.Handler.getHandler(a(this));v&&v.unregisterForm()});var n=f.eq(0).index();e.tabs("option","active")==n&&e.tabs("option","active",n-1);f.remove();k.remove();e.tabs("refresh")}});e.children("ul").append(c);e.tabs("refresh");e.tabs("option","active",b-1)};a.pkp.controllers.TabHandler.prototype.getCurrentTab=function(){return this.$currentTab_};a.pkp.controllers.TabHandler.prototype.getCurrentTabIndex=function(){return this.currentTabIndex_}})(jQuery);(function(a){a.pkp.controllers.MenuHandler=function(b,c){this.parent(b,c);var d=this;setTimeout(function(){d.callbackWrapper(d.setDropdownAlignment())},1);a(window).resize(this.callbackWrapper(this.onResize));this.getHtmlElement().on("focus mouseenter",'[aria-haspopup="true"]',function(e){a(e.currentTarget).attr("aria-expanded","true")});this.getHtmlElement().on("blur mouseleave",'[aria-haspopup="true"]',function(e){a(e.currentTarget).attr("aria-expanded","false")})};a.pkp.classes.Helper.inherits(a.pkp.controllers.MenuHandler,
a.pkp.classes.Handler);a.pkp.controllers.MenuHandler.prototype.setDropdownAlignment=function(){a(this);var b=Math.max(document.documentElement.clientWidth,window.innerWidth||0),c=Math.max(document.documentElement.clientHeight,window.innerHeight||0);this.getHtmlElement().find('[aria-haspopup="true"]').each(function(){var d=a(this),e=d.children("ul");d.offset().left+e.outerWidth()>b?d.addClass("align_right"):d.removeClass("align_right");e.attr("style","");var f=d.offset().top;var k=0;d.hasClass("submenuOpensBelow")&&
(k=f+d.outerHeight());var g=f+e.outerHeight();if(g>c){g-=c;var n=f-g;n<k&&(g=0<k?k:-Math.abs(g)-n,e.css("overflow-y","scroll"),e.css("bottom",-Math.abs(c-f-d.outerHeight())+"px"));e.css("top",g+"px")}})};a.pkp.controllers.MenuHandler.prototype.onResize=function(){clearTimeout(this.resize_check);this.resize_check=setTimeout(this.callbackWrapper(this.setDropdownAlignment),1E3)}})(jQuery);(function(a){a.pkp.controllers.UploaderHandler=function(b,c){this.parent(b,c);if(!b.is("div"))throw Error("An uploader widget controller can only be attached to a div!");var d={url:c.uploadUrl,flash_swf_url:c.baseUrl+"/lib/pkp/lib/vendor/moxiecode/plupload/js/Moxie.swf",silverlight_xap_url:c.baseUrl+"/lib/pkp/lib/vendor/moxiecode/plupload/js/Moxie.xap"};typeof c.filters&&(d.filters=c.filters);typeof c.resize&&(d.resize=c.resize);typeof c.browse_button&&(d.browse_button=c.browse_button);typeof c.multipart_params&&
(d.multipart_params=c.multipart_params);d.drop_element=b.first().find("#"+b.first().attr("id")+"-pkpUploaderDropZone").attr("id");d=a.extend({},this.self("DEFAULT_PROPERTIES_"),d);this.pluploader=new plupload.Uploader(d);this.pluploader.init();this.updateStatus("waiting");this.$progress=b.find(".pkpUploaderProgress .percentage");this.$progressBar=b.find(".pkpUploaderProgressBar");this.$fileName=b.find(".pkpUploaderFilename");this.pluploader.bind("FilesAdded",this.callbackWrapper(this.startUpload));
this.pluploader.bind("UploadProgress",this.callbackWrapper(this.updateProgress));this.pluploader.bind("Error",this.callbackWrapper(this.handleError));this.pluploader.bind("FileUploaded",this.callbackWrapper(this.uploadComplete));this.pluploader.bind("QueueChanged",this.callbackWrapper(this.refreshUploader));var e=a("#"+d.browse_button,this.getHtmlElement());e.click(function(k){return!1});this.pluploader.refresh();var f=this;setTimeout(function(){f.getHtmlElement().find(".moxie-shim input").focus(function(k){e.addClass("in_focus")}).blur(function(k){e.removeClass("in_focus")})},
100)};a.pkp.classes.Helper.inherits(a.pkp.controllers.UploaderHandler,a.pkp.classes.Handler);a.pkp.controllers.UploaderHandler.prototype.startUpload=function(b,c,d){1<c.files.length&&c.removeFile(c.files[0]);this.updateStatus("uploading");c.start()};a.pkp.controllers.UploaderHandler.prototype.updateProgress=function(b,c,d){this.$progress.html(d.percent);this.$progressBar.css("width",d.percent+"%")};a.pkp.controllers.UploaderHandler.prototype.uploadComplete=function(b,c,d,e){b=a.parseJSON(e.response);
c=d.name;b.status?("undefined"!==typeof b.uploadedFile&&(c=b.uploadedFile.name||b.uploadedFile.fileLabel,d.storedData=b.uploadedFile),this.$fileName.text(c),this.updateStatus("complete"),this.$progress.html("0"),this.$progressBar.css("width",0)):this.showError(b.content)};a.pkp.controllers.UploaderHandler.prototype.handleError=function(b,c,d){this.showError(d.message)};a.pkp.controllers.UploaderHandler.prototype.showError=function(b){this.$progress.html("0");this.$progressBar.css("width",0);this.updateStatus("error");
this.getHtmlElement().find(".pkpUploaderError").html(b)};a.pkp.controllers.UploaderHandler.prototype.refreshUploader=function(b,c,d){c.refresh()};a.pkp.controllers.UploaderHandler.prototype.updateStatus=function(b){this.getHtmlElement().removeClass("loading waiting uploading error complete").addClass(b)};a.pkp.controllers.UploaderHandler.DEFAULT_PROPERTIES_={runtimes:"html5,flash,silverlight,html4",max_file_size:a.pkp.cons.UPLOAD_MAX_FILESIZE,multi_selection:!1,file_data_name:"uploadedFile",multipart:!0,
headers:{browser_user_agent:navigator.userAgent},browse_button:"pkpUploaderButton"}})(jQuery);(function(a){a.pkp.controllers.AutocompleteHandler=function(b,c){this.parent(b,c);this.sourceUrl_=c.sourceUrl;c.sourceUrl=void 0;this.disableSync_=c.disableSync;var d={};d.source=this.callbackWrapper(this.fetchAutocomplete);d.appendTo="#"+b.attr("id");c=a.extend({},this.self("DEFAULT_PROPERTIES_"),d,c);this.textInput=b.find(":text");this.disableSync_||this.textInput.keyup(this.callbackWrapper(this.synchronizeFields_));this.textInput.autocomplete(c);this.hiddenInput_=b.find("input:hidden");this.bind("autocompleteselect",
this.itemSelected);this.bind("autocompletefocus",this.itemFocused);this.textInput.blur(this.callbackWrapper(this.textInputBlurHandler_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.AutocompleteHandler,a.pkp.classes.Handler);a.pkp.controllers.AutocompleteHandler.disableSync_=null;a.pkp.controllers.AutocompleteHandler.DEFAULT_PROPERTIES_={minLength:2};a.pkp.controllers.AutocompleteHandler.prototype.hiddenInput_=null;a.pkp.controllers.AutocompleteHandler.prototype.sourceUrl_=null;a.pkp.controllers.AutocompleteHandler.prototype.textInput=
null;a.pkp.controllers.AutocompleteHandler.prototype.itemSelected=function(b,c,d){b=this.hiddenInput_;c=this.textInput;""!==d.item.value&&(b.val(d.item.value),c.val(d.item.label));return!1};a.pkp.controllers.AutocompleteHandler.prototype.itemFocused=function(b,c,d){b=this.textInput;""!==d.item.value&&b.val(d.item.label);return!1};a.pkp.controllers.AutocompleteHandler.prototype.fetchAutocomplete=function(b,c,d){var e=this.textInput;e.addClass("spinner");a.post(this.getAutocompleteUrl(),{term:c.term},
function(f){e.removeClass("spinner");d(f.content)},"json")};a.pkp.controllers.AutocompleteHandler.prototype.getAutocompleteUrl=function(){return this.sourceUrl_};a.pkp.controllers.AutocompleteHandler.prototype.setAutocompleteUrl=function(b){this.sourceUrl_=b};a.pkp.controllers.AutocompleteHandler.prototype.textInputBlurHandler_=function(b,c,d){""===this.hiddenInput_.val()&&this.textInput.val("")};a.pkp.controllers.AutocompleteHandler.prototype.synchronizeFields_=function(){this.hiddenInput_.val(String(this.textInput.val()))}})(jQuery);(function(a){a.pkp.controllers.RevealMoreHandler=function(b,c){this.parent(b,c);b.outerHeight()>c.height&&(b.addClass("isHidden").css("max-height",c.height+"px"),a(".revealMoreButton",b).click(this.callbackWrapper(this.revealMore)))};a.pkp.classes.Helper.inherits(a.pkp.controllers.RevealMoreHandler,a.pkp.classes.Handler);a.pkp.controllers.RevealMoreHandler.prototype.revealMore=function(b,c){this.getHtmlElement().removeClass("isHidden").removeAttr("style");c.preventDefault();c.stopPropagation()}})(jQuery);(function(a){a.pkp.controllers.NotificationHandler=function(b,c){this.parent(b,c);this.options_=c;this.bind("notifyUser",this.fetchNotificationHandler_);this.getHtmlElement().hide();this.getHtmlElement().triggerHandler("notifyUser");this.options_.refreshOn&&this.bindGlobal(this.options_.refreshOn,this.fetchNotificationHandler_)};a.pkp.classes.Helper.inherits(a.pkp.controllers.NotificationHandler,a.pkp.classes.Handler);a.pkp.controllers.NotificationHandler.prototype.options_=null;a.pkp.controllers.NotificationHandler.prototype.trivialTimer_=
null;a.pkp.controllers.NotificationHandler.prototype.fetchNotificationHandler_=function(){var b={};b.requestOptions=this.options_.requestOptions;a.ajax({type:"POST",url:this.options_.fetchNotificationUrl,data:b,success:this.callbackWrapper(this.showNotificationResponseHandler_),dataType:"json",async:!1})};a.pkp.controllers.NotificationHandler.prototype.showNotificationResponseHandler_=function(b,c){clearTimeout(this.trivialTimer_);b=this.getHtmlElement();var d=this.handleJson(c),e;if(!1!==d)if(d.content.inPlace){var f=
this.concatenateNotifications_(d.content.inPlace);c=this.removeAlreadyShownNotifications_(d);this.unbindPartial(b);b.html(f);b.show();d=this.getTrivialNotifications_(d.content.inPlace);if(!this.visibleWithoutScrolling_()&&c)for(e in b.parent().trigger("notifyUser",[c]),d)b=d[e],b=a("#pkp_notification_"+b,this.getHtmlElement()),this.unbindPartial(b),b.remove();0===b.children().length?b.hide():this.addTimerToNotifications(d)}else this.unbindPartial(this.getHtmlElement()),this.getHtmlElement().empty(),
this.getHtmlElement().hide()};a.pkp.controllers.NotificationHandler.prototype.visibleWithoutScrolling_=function(){var b=this.getHtmlElement(),c=b.offset().top+this.getHtmlElement().height()/2,d=a(window).scrollTop(),e=d+a(window).height(),f=b.parents(".ui-dialog-content");return 0<f.length&&(b=f.offset().top,f=b+f.height(),c<b||c>f)?!1:c<d||c>e?!1:!0};a.pkp.controllers.NotificationHandler.prototype.removeAlreadyShownNotifications_=function(b){var c=!0,d,e;for(d in b.content.inPlace)for(e in b.content.inPlace[d]){var f=
a("#pkp_notification_"+e);0<f.length?(delete b.content.inPlace[d][e],delete b.content.general[d][e]):c=!1}return c?!1:b};a.pkp.controllers.NotificationHandler.prototype.concatenateNotifications_=function(b){var c="",d,e;for(d in b)for(e in b[d])c+=b[d][e];return c};a.pkp.controllers.NotificationHandler.prototype.getTrivialNotifications_=function(b){var c=[],d,e;for(d in b)if(1==d)for(e in b[d])c.push(e);return c};a.pkp.controllers.NotificationHandler.prototype.addTimerToNotifications=function(b){var c=
this;var d=function(){var e=a(this);c.unbindPartial(e);e.remove()};b.length&&(this.trivialTimer_=setTimeout(function(){var e;for(e in b){var f=a("#pkp_notification_"+b[e]);f.fadeOut(400,d)}},6E3))}})(jQuery);(function(a){a.pkp.controllers.HelpPanelHandler=function(b,c){this.parent(b,{});a("body").click(function(d){var e=a(d.target);if(e.hasClass("requestHelpPanel")||e.parents(".requestHelpPanel").length)d.preventDefault(),d=a.extend({},e.data(),{caller:e}),b.trigger("pkp.HelpPanel.Open",d)});b.find(".pkpCloseHelpPanel").click(function(d){d.preventDefault();b.trigger("pkp.HelpPanel.Close")});b.find(".pkpHomeHelpPanel").click(function(d){d.preventDefault();b.trigger("pkp.HelpPanel.Home")});b.find(".pkpPreviousHelpPanel").click(this.callbackWrapper(function(d){this.loadHelpContent_(this.previousTopic_,
this.helpLocale_)}));b.find(".pkpNextHelpPanel").click(this.callbackWrapper(function(d){this.loadHelpContent_(this.nextTopic_,this.helpLocale_)}));b.on("pkp.HelpPanel.Open",this.callbackWrapper(this.openPanel_)).on("pkp.HelpPanel.Close",this.callbackWrapper(this.closePanel_)).on("pkp.HelpPanel.Home",this.callbackWrapper(this.homePanel_));this.helpUrl_=c.helpUrl;this.helpLocale_=c.helpLocale};a.pkp.classes.Helper.inherits(a.pkp.controllers.HelpPanelHandler,a.pkp.classes.Handler);a.pkp.controllers.HelpPanelHandler.prototype.caller_=
null;a.pkp.controllers.HelpPanelHandler.prototype.helpUrl_=null;a.pkp.controllers.HelpPanelHandler.prototype.helpLocale_=null;a.pkp.controllers.HelpPanelHandler.prototype.currentTopic_=null;a.pkp.controllers.HelpPanelHandler.prototype.previousTopic_=null;a.pkp.controllers.HelpPanelHandler.prototype.nextTopic_=null;a.pkp.controllers.HelpPanelHandler.prototype.requestedSection_=null;a.pkp.controllers.HelpPanelHandler.prototype.openPanel_=function(b,c,d){var e=this.getHtmlElement();"undefined"!==typeof d.caller&&
(this.caller_=d.caller);e.addClass("is_visible");a("body").addClass("help_panel_is_visible");e.on("click.pkp.HelpPanel keyup.pkp.HelpPanel",this.callbackWrapper(this.handleWrapperEvents));e.on("click.pkp.HelpPanelContentLink",".content a",this.callbackWrapper(this.handleContentLinks_));this.loadHelpContent_(d.topic,this.helpLocale_);this.requestedSection_=d.section||"";setTimeout(function(){e.focus()},300)};a.pkp.controllers.HelpPanelHandler.prototype.loadHelpContent_=function(b,c){c=c||this.helpLocale_;
this.currentTopic_=b||"";b=this.helpUrl_+"/index/"+c+"/";this.getHtmlElement().addClass("is_loading");b+=encodeURIComponent(this.currentTopic_).replace(/%2F/g,"/");a.get(b,null,this.callbackWrapper(this.updateContentHandler_),"json")};a.pkp.controllers.HelpPanelHandler.prototype.updateContentHandler_=function(b,c){c=this.handleJson(c).content;var d=this.getHtmlElement();this.currentTopic_.indexOf("#");b=d.find(".panel");this.previousTopic_=c.previous;this.nextTopic_=c.next;d.find(".content").replaceWith('<div class="content">'+
c.content+"</div>");b.scrollTop(0);this.requestedSection_&&(c=d.find('a[name="'+this.requestedSection_+'"]'),c.length&&b.scrollTop(c.offset().top-50));this.getHtmlElement().removeClass("is_loading")};a.pkp.controllers.HelpPanelHandler.prototype.handleContentLinks_=function(b,c){b=a(b).attr("href");c.preventDefault();if("http"==b.substring(0,4))return window.open(b),!1;b=b.split("/");c=b.slice(1).join("/");b=b[0];if(-1<c.indexOf("#")){var d=c.split("#");c=d[0];this.requestedSection_=d[1]}this.loadHelpContent_(c,
b);return!1};a.pkp.controllers.HelpPanelHandler.prototype.closePanel_=function(){var b=this.getHtmlElement();b.removeClass("is_visible");a("body").removeClass("help_panel_is_visible");b.find(".content").empty();null!==this.caller_&&this.caller_.focus();b.off("click.pkp.HelpPanel keyup.pkp.HelpPanel");b.off("click.pkp.HelpPanelContentLink",".content a");this.caller_=null};a.pkp.controllers.HelpPanelHandler.prototype.homePanel_=function(){this.loadHelpContent_(null,this.helpLocale_)};a.pkp.controllers.HelpPanelHandler.prototype.handleWrapperEvents=
function(b,c){b=this.getHtmlElement();"click"==c.type&&b.is(a(c.target))?b.trigger("pkp.HelpPanel.Close"):"keyup"==c.type&&27==c.which&&b.trigger("pkp.HelpPanel.Close")}})(jQuery);(function(a){a.pkp.controllers.form=a.pkp.controllers.form||{};a.pkp.controllers.form.FormHandler=function(b,c){var d;this.parent(b,c);if(!b.is("form"))throw Error("A form handler controller can only be bound to an HTML form element!");c.submitHandler&&(this.callerSubmitHandler_=c.submitHandler);b.find(".datepicker").each(function(){var e=a(this);e.datepicker({changeMonth:!0,changeYear:!0,altField:"#"+e.prop("id")+"-altField",altFormat:"yy-mm-dd",dateFormat:a("#"+e.prop("id")+"-altField").attr("data-date-format")});
e.prop("name",e.prop("name")+"-removed")});c.cancelRedirectUrl&&(this.cancelRedirectUrl_=c.cancelRedirectUrl);"undefined"!==typeof c.trackFormChanges&&(this.trackFormChanges=c.trackFormChanges);c.disableControlsOnSubmit&&(this.disableControlsOnSubmit=c.disableControlsOnSubmit);c.enableDisablePairs&&(this.enableDisablePairs_=c.enableDisablePairs,this.setupEnableDisablePairs());for(d in this.enableDisablePairs_)b.find("[id^='"+d+"']").trigger("updatePair");c=b.validate({onfocusout:this.callbackWrapper(this.onFocusOutValidation_),
errorClass:"error",highlight:function(e,f){a(e).parent().parent().addClass(f)},unhighlight:function(e,f){a(e).parent().parent().removeClass(f)},submitHandler:this.callbackWrapper(this.submitHandler_),showErrors:this.callbackWrapper(this.showErrors),errorPlacement:function(e,f){f.is(":checkbox")?f.parent().closest(":not(label)").append(e):e.insertAfter(f)}});a("[id^='cancelFormButton-']",b).click(this.callbackWrapper(this.cancelForm));b.find(".showMore, .showLess").bind("click",this.switchViz);c.checkForm()?
this.trigger("formValid"):this.trigger("formInvalid");this.initializeTinyMCE();a("[id^='submitFormButton']",b).click(this.callbackWrapper(this.pushTinyMCEChanges_));a(":input",b).change(this.callbackWrapper(this.formChange));this.bind("formChange",this.callbackWrapper(this.formChange));b.click(this.callbackWrapper(this.hideDatepicker_));this.publishEvent("tinyMCEInitialized");this.bind("tinyMCEInitialized",this.tinyMCEInitHandler_);this.bind("containerClose",this.containerCloseHandler)};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.FormHandler,
a.pkp.classes.Handler);a.pkp.controllers.form.FormHandler.prototype.disableControlsOnSubmit=!1;a.pkp.controllers.form.FormHandler.prototype.trackFormChanges=!0;a.pkp.controllers.form.FormHandler.prototype.callerSubmitHandler_=null;a.pkp.controllers.form.FormHandler.prototype.cancelRedirectUrl_=null;a.pkp.controllers.form.FormHandler.prototype.formChangesTracked=!1;a.pkp.controllers.form.FormHandler.prototype.enableDisablePairs_=null;a.pkp.controllers.form.FormHandler.prototype.showErrors=function(b,
c,d){"undefined"!==typeof tinyMCE&&tinyMCE.EditorManager.triggerSave();var e=a.extend(!0,{},b);setTimeout(this.callbackWrapper(function(){e.defaultShowErrors();e=null}),250);b.checkForm()?this.trigger("formValid"):(this.trigger("formInvalid"),this.enableFormControls())};a.pkp.controllers.form.FormHandler.prototype.formChange=function(b,c){this.trackFormChanges&&!this.formChangesTracked&&(this.formChangesTracked=!0,this.trigger("formChanged"))};a.pkp.controllers.form.FormHandler.prototype.disableFormControls=
function(){this.disableControlsOnSubmit&&this.getHtmlElement().find(":submit").attr("disabled","disabled").addClass("ui-state-disabled");return!0};a.pkp.controllers.form.FormHandler.prototype.enableFormControls=function(){this.getHtmlElement().find(":submit").removeAttr("disabled").removeClass("ui-state-disabled");return!0};a.pkp.controllers.form.FormHandler.prototype.cancelForm=function(b,c){this.unregisterForm();this.trigger("formCanceled");return!1};a.pkp.controllers.form.FormHandler.prototype.unregisterForm=
function(){this.formChangesTracked=!1;this.trigger("unregisterChangedForm")};a.pkp.controllers.form.FormHandler.prototype.setupEnableDisablePairs=function(){var b=this.getHtmlElement(),c;for(c in this.enableDisablePairs_)a(b).find("[id^='"+c+"']").bind("click updatePair",this.callbackWrapper(this.toggleDependentElement_));return!0};a.pkp.controllers.form.FormHandler.prototype.submitFormWithoutValidation=function(b){b.settings.submitHandler=null;this.disableFormControls();this.getHtmlElement().submit();
this.formChangesTracked=!1};a.pkp.controllers.form.FormHandler.prototype.hideSpinner=function(){this.getHtmlElement().find(".formButtons .pkp_spinner").removeClass("is_visible")};a.pkp.controllers.form.FormHandler.prototype.submitHandler_=function(b,c){var d=!1;a(c).find(".formWidget").each(function(){var e=new a.Event("formSubmitRequested");d||(a(this).trigger(e),d=e.isDefaultPrevented())});d||(a(c).find(".datepicker").each(function(){""===a(this).prop("value")&&a("#"+a(this).prop("id")+"-altField").prop("value",
"")}),this.showSpinner_(),this.trigger("unregisterChangedForm"),null!==this.callerSubmitHandler_?(this.formChangesTracked=!1,this.callbackWrapper(this.callerSubmitHandler_).call(b,c)):this.submitFormWithoutValidation(b))};a.pkp.controllers.form.FormHandler.prototype.pushTinyMCEChanges_=function(b,c){"undefined"!==typeof tinyMCE&&tinyMCE.EditorManager.triggerSave();return!0};a.pkp.controllers.form.FormHandler.prototype.toggleDependentElement_=function(b,c){c=this.getHtmlElement();var d=a(b).attr("id");
c=a(c).find("[id^='"+this.enableDisablePairs_[d]+"']");a(b).is(":checked")?a(c).prop("disabled",!1):a(c).prop("disabled",!0);return!0};a.pkp.controllers.form.FormHandler.prototype.tinyMCEInitHandler_=function(b,c,d){var e=d.id;d.on("blur",this.callbackWrapper(function(f){f.save();f=this.getHtmlElement();var k=a("#"+a.pkp.classes.Helper.escapeJQuerySelector(e),f);f.validate().element(k)}))};a.pkp.controllers.form.FormHandler.prototype.containerCloseHandler=function(b,c,d){a(this.getHtmlElement()).find(".hasDatepicker").datepicker("hide");
if(this.formChangesTracked)if(confirm(pkp.localeKeys["form.dataHasChanged"]))this.trigger("unregisterAllForms");else return d&&(d.closePermitted=!1),!1;"undefined"!==typeof d&&(d.closePermitted=!0);return!0};a.pkp.controllers.form.FormHandler.prototype.onFocusOutValidation_=function(b,c){var d=this.getHtmlElement();a(c).parents("#"+d.attr("id")).length&&b.element(c);return!0};a.pkp.controllers.form.FormHandler.prototype.hideDatepicker_=function(b,c){b=c.originalEvent;"undefined"!=typeof b&&(b=b.target,
c=this.getHtmlElement(),a(b).hasClass("hasDatepicker")||a(b).hasClass("ui-datepicker")||a(b).hasClass("ui-icon")||a(b).hasClass("ui-datepicker-next")||a(b).hasClass("ui-datepicker-prev")||a(b).parent().parents(".ui-datepicker").length||a(c).find(".hasDatepicker").datepicker("hide"))};a.pkp.controllers.form.FormHandler.prototype.showSpinner_=function(){this.getHtmlElement().find(".formButtons .pkp_spinner").addClass("is_visible")}})(jQuery);(function(a){a.pkp.controllers.form.AjaxFormHandler=function(b,c){c.submitHandler=this.submitForm;this.parent(b,c);"undefined"!==typeof c.confirmText&&(this.confirmText=c.confirmText);this.bind("refreshForm",this.refreshFormHandler_)};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.AjaxFormHandler,a.pkp.controllers.form.FormHandler);a.pkp.controllers.form.AjaxFormHandler.prototype.disableControlsOnSubmit=!0;a.pkp.controllers.form.AjaxFormHandler.prototype.confirmText="";a.pkp.controllers.form.AjaxFormHandler.prototype.submitForm=
function(b,c){b=this.getHtmlElement();this.disableFormControls();this.confirmText.length&&!confirm(this.confirmText)||a.post(b.attr("action"),b.serialize(),this.callbackWrapper(this.handleResponse),"json")};a.pkp.controllers.form.AjaxFormHandler.prototype.refreshFormHandler_=function(b,c,d){d&&this.replaceWith(d)};a.pkp.controllers.form.AjaxFormHandler.prototype.handleResponse=function(b,c){c=this.handleJson(c);!1!==c?""===c.content?(b=new a.Event("formSubmitted"),a(this.getHtmlElement()).find(".formWidget").trigger(b),
this.trigger("formSubmitted"),this.publishChangeEvents(),this.disableControlsOnSubmit&&this.enableFormControls()):this.replaceWith(c.content):this.enableFormControls();this.trigger("notifyUser");this.hideSpinner();return c.status}})(jQuery);(function(a){a.pkp.controllers.form.ClientFormHandler=function(b,c){c.submitHandler=this.submitForm;this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.ClientFormHandler,a.pkp.controllers.form.FormHandler);a.pkp.controllers.form.ClientFormHandler.prototype.submitForm=function(b,c){b=this.getHtmlElement().serializeArray();b.push({name:"clientSubmit",value:!0});this.trigger("formSubmitted",[a.param(b)])}})(jQuery);(function(a){a.pkp.controllers.form.ToggleFormHandler=function(b){this.parent(b,{trackFormChanges:!1});b.change(this.callbackWrapper(this.toggleHandler_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.ToggleFormHandler,a.pkp.controllers.form.ClientFormHandler);a.pkp.controllers.form.ToggleFormHandler.prototype.toggleHandler_=function(){this.getHtmlElement().submit();return!0}})(jQuery);(function(a){a.pkp.controllers.form.FileUploadFormHandler=function(b,c){this.parent(b,c);if(void 0===c.readOnly||null===c.readOnly)void 0!==c.resetUploader&&(this.resetUploader_=c.resetUploader),void 0!==c.$preview&&c.$preview.length&&(this.$preview=c.$preview,this.bind("fileDeleted",this.callbackWrapper(this.fileDeleted))),this.attachUploader_(c.$uploader,c.uploaderOptions),this.uploaderSetup(c.$uploader)};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.FileUploadFormHandler,a.pkp.controllers.form.AjaxFormHandler);
a.pkp.controllers.form.FileUploadFormHandler.prototype.resetUploader_=!1;a.pkp.controllers.form.FileUploadFormHandler.prototype.$preview=!1;a.pkp.controllers.form.FileUploadFormHandler.prototype.handleResponse=function(b,c){if(this.resetUploader_){var d=a("#plupload",this.getHtmlElement()).plupload("getUploader");d.splice();d.refresh();a("#temporaryFileId",this.getHtmlElement()).val("")}return this.parent("handleResponse",b,c)};a.pkp.controllers.form.FileUploadFormHandler.prototype.uploaderSetup=
function(b){a.pkp.classes.Handler.getHandler(b).pluploader.bind("FileUploaded",this.callbackWrapper(this.handleUploadResponse))};a.pkp.controllers.form.FileUploadFormHandler.prototype.handleUploadResponse=function(b,c,d,e){b=this.handleJson(a.parseJSON(e.response));!1!==b&&(this.trigger("fileUploaded",[b.uploadedFile]),this.$preview&&this.$preview.hide(),""===b.content?(c=this.getHtmlElement(),c=c.find("#temporaryFileId"),c.val(b.temporaryFileId)):this.replaceWith(b.content))};a.pkp.controllers.form.FileUploadFormHandler.prototype.fileDeleted=
function(){this.$preview&&this.$preview.hide()};a.pkp.controllers.form.FileUploadFormHandler.prototype.attachUploader_=function(b,c){b.pkpHandler("$.pkp.controllers.UploaderHandler",c)}})(jQuery);(function(a){a.pkp.controllers.form.MultilingualInputHandler=function(b,c){this.parent(b,c);(b.hasClass("pkpTagit")?b.find(":input").filter(":visible"):b.find(":input").first()).focus(this.callbackWrapper(this.focusHandler_));b.find(":input").blur(this.callbackWrapper(this.blurHandler_));this.publishEvent("tinyMCEInitialized");this.tinyMCEInitHandler_();setTimeout(this.callbackWrapper(this.isIncomplete_),500)};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.MultilingualInputHandler,a.pkp.classes.Handler);
a.pkp.controllers.form.MultilingualInputHandler.prototype.focusHandler_=function(b,c){this.showPopover_()};a.pkp.controllers.form.MultilingualInputHandler.prototype.blurHandler_=function(b,c){setTimeout(this.callbackWrapper(function(){this.hasElementInFocus_()||this.hidePopover_()}),100);return!0};a.pkp.controllers.form.MultilingualInputHandler.prototype.hidePopover_=function(){var b=this.getHtmlElement();b.removeClass("localization_popover_container_focus");b.find(".localization_popover").hide();
this.isIncomplete_()};a.pkp.controllers.form.MultilingualInputHandler.prototype.showPopover_=function(){var b=this.getHtmlElement();b.addClass("localization_popover_container_focus");b.find(".localization_popover").width(b.width());b.find(".localization_popover").show()};a.pkp.controllers.form.MultilingualInputHandler.prototype.hasElementInFocus_=function(){return this.getHtmlElement().has(document.activeElement).length?!0:!1};a.pkp.controllers.form.MultilingualInputHandler.prototype.isIncomplete_=
function(){var b=this.getHtmlElement(),c=[],d=0;"undefined"!==typeof tinyMCE&&(this.getHtmlElement().find(".richContent").length?b.find("textarea").each(function(){a(this).attr("id");c.push(a(this));tinyMCE.EditorManager.get(a(this).attr("id")).getContent()&&d++}):(c=b.find(":input"),c.each(function(){a(this).val()&&d++})),0<d&&d<c.length?b.removeClass("localizationComplete").addClass("localizationIncomplete"):d===c.length?b.removeClass("localizationIncomplete").addClass("localizationComplete"):b.removeClass("localizationIncomplete localizationComplete"))};
a.pkp.controllers.form.MultilingualInputHandler.prototype.tinyMCEInitHandler_=function(){if(this.getHtmlElement().find(".richContent").length&&"undefined"!==typeof tinyMCE){var b=this.getHtmlElement(),c=tinyMCE.EditorManager.get(b.find("textarea").first().attr("id"));c.on("focus",this.callbackWrapper(function(){this.trigger("callWhenClickOutside",{container:this.getHtmlElement(),callback:this.callbackWrapper(this.hidePopover_)});this.showPopover_()}));c.on("blur",this.callbackWrapper(function(){if(a(c.getContainer()).find("iframe").attr("id")==
a(document.activeElement).attr("id"))return!1;setTimeout(this.callbackWrapper(function(){this.hasElementInFocus_()||this.hidePopover_()}),0)}))}}})(jQuery);(function(a){a.pkp.controllers.form.CancelActionAjaxFormHandler=function(b,c){var d=this;this.parent(b,c);this.cancelUrl_=c.cancelUrl;this.cancelActionHandler=function(){d.handleCancelAction()};a(window).on("unload",this.cancelActionHandler)};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.CancelActionAjaxFormHandler,a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.form.CancelActionAjaxFormHandler.prototype.cancelActionHandler=null;a.pkp.controllers.form.CancelActionAjaxFormHandler.prototype.cancelUrl_=
null;a.pkp.controllers.form.CancelActionAjaxFormHandler.prototype.isComplete_=!1;a.pkp.controllers.form.CancelActionAjaxFormHandler.prototype.containerCloseHandler=function(b,c,d){this.parent("containerCloseHandler",b,c,d)&&this.handleCancelAction()};a.pkp.controllers.form.CancelActionAjaxFormHandler.prototype.submitForm=function(b,c){this.isComplete_=!0;this.parent("submitForm",b,c)};a.pkp.controllers.form.CancelActionAjaxFormHandler.prototype.handleCancelAction=function(){null!==this.cancelActionHandler&&
(a(window).off("unload",this.cancelActionHandler),this.cancelActionHandler=null);this.isComplete_||null===this.cancelUrl_||a.post(this.cancelUrl_)}})(jQuery);(function(a){a.pkp.controllers.form.UserFormHandler=function(b,c){this.parent(b,c);c.fetchUsernameSuggestionUrl&&c.usernameSuggestionTextAlert&&(this.fetchUsernameSuggestionUrl_=c.fetchUsernameSuggestionUrl,this.usernameSuggestionTextAlert_=c.usernameSuggestionTextAlert);a('[id^="suggestUsernameButton"]',b).click(this.callbackWrapper(this.generateUsername));c.hideNonReviewerInterests&&(a('[id^="reviewerGroup-"]',b).click(this.callbackWrapper(this.setInterestsVisibility_)),this.setInterestsVisibility_())};
a.pkp.classes.Helper.inherits(a.pkp.controllers.form.UserFormHandler,a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.form.UserFormHandler.prototype.fetchUsernameSuggestionUrl_="";a.pkp.controllers.form.UserFormHandler.prototype.usernameSuggestionTextAlert_="";a.pkp.controllers.form.UserFormHandler.prototype.generateUsername=function(b,c){c.preventDefault();c=this.getHtmlElement();var d=a('[name="sitePrimaryLocale"]',c).val();b=a('[name="givenName['+d+']"]',c).val();c=a('[name="familyName['+
d+']"]',c).val();b=this.fetchUsernameSuggestionUrl_.replace("GIVEN_NAME_PLACEHOLDER",b).replace("FAMILY_NAME_PLACEHOLDER",c);a.get(b,this.callbackWrapper(this.setUsername),"json")};a.pkp.controllers.form.UserFormHandler.prototype.setUsername=function(b,c){b=this.handleJson(c);c=this.getHtmlElement();if(!1===b)throw Error("JSON response must be set to true!");a('[id^="username"]',c).val(b.content).trigger("blur")};a.pkp.controllers.form.UserFormHandler.prototype.setInterestsVisibility_=function(){var b=
this.getHtmlElement(),c=a("#interests",b);a('[id^="reviewerGroup-"]:checked',b).size()?c.show(300):c.hide(300)}})(jQuery);(function(a){a.pkp.controllers.form.reviewer=a.pkp.controllers.form.reviewer||{};a.pkp.controllers.form.reviewer.ReviewerReviewStep3FormHandler=function(b,c){this.parent(b,c);b.find("[id^='submitFormButton-']").click(this.callbackWrapper(this.updateCommentsRequired_));b.find("[type^='submit']").click(this.callbackWrapper(this.updateRecommendationRequired_));b.find("[type^='submit']").click(this.callbackWrapper(this.updateSaveOrSubmit_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.form.reviewer.ReviewerReviewStep3FormHandler,
a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.form.reviewer.ReviewerReviewStep3FormHandler.prototype.updateRecommendationRequired_=function(b,c){c=this.getHtmlElement().find('[id^="recommendation"]');c.length&&(b.id.includes("submitFormButton-")?c.attr("required","1"):c.removeAttr("required"));return!0};a.pkp.controllers.form.reviewer.ReviewerReviewStep3FormHandler.prototype.updateCommentsRequired_=function(b,c){b=this.getHtmlElement().find('[id^="comments"]');1==a("#reviewAttachmentsGridContainer").find("tbody.empty:visible").length?
b.attr("required","1"):b.removeAttr("required");return!0};a.pkp.controllers.form.reviewer.ReviewerReviewStep3FormHandler.prototype.updateSaveOrSubmit_=function(b,c){c=this.getHtmlElement();switch(a(b).attr("name")){case "submitFormButton":c.find('input[name="isSave"]').val("0");break;case "saveFormButton":c.find('input[name="isSave"]').val("1")}return!0}})(jQuery);(function(a){a.pkp.controllers.grid=a.pkp.controllers.grid||{};a.pkp.controllers.grid.GridHandler=function(b,c){this.parent(b,c);this.initialize(c);this.initFeatures_(c.features)};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.GridHandler,a.pkp.classes.Handler);a.pkp.controllers.grid.GridHandler.FETCH_ALL_ROWS_ID={};a.pkp.controllers.grid.GridHandler.prototype.bodySelector=null;a.pkp.controllers.grid.GridHandler.prototype.fetchRowUrl=null;a.pkp.controllers.grid.GridHandler.prototype.fetchRowsUrl=
null;a.pkp.controllers.grid.GridHandler.prototype.gridId_=null;a.pkp.controllers.grid.GridHandler.prototype.fetchGridUrl_=null;a.pkp.controllers.grid.GridHandler.prototype.features_=null;a.pkp.controllers.grid.GridHandler.prototype.fetchExtraParams_=null;a.pkp.controllers.grid.GridHandler.prototype.getFetchExtraParams=function(){return this.fetchExtraParams_};a.pkp.controllers.grid.GridHandler.prototype.setFetchExtraParams=function(b){this.fetchExtraParams_=b};a.pkp.controllers.grid.GridHandler.prototype.getFetchRowUrl=
function(){return this.fetchRowUrl};a.pkp.controllers.grid.GridHandler.prototype.getFetchRowsUrl=function(){return this.fetchRowsUrl};a.pkp.controllers.grid.GridHandler.prototype.getRows=function(){return a(".gridRow",this.getHtmlElement()).not(".gridRowDeleted")};a.pkp.controllers.grid.GridHandler.prototype.getGridIdPrefix=function(){return"component-"+this.gridId_};a.pkp.controllers.grid.GridHandler.prototype.getRowIdPrefix=function(){return this.getGridIdPrefix()+"-row-"};a.pkp.controllers.grid.GridHandler.prototype.getRowByDataId=
function(b,c){return a("#"+this.getRowIdPrefix()+a.pkp.classes.Helper.escapeJQuerySelector(String(b)),this.getHtmlElement())};a.pkp.controllers.grid.GridHandler.prototype.getRowDataId=function(b){return b.attr("id").slice(this.getRowIdPrefix().length)};a.pkp.controllers.grid.GridHandler.prototype.getParentRow=function(b){return b.parents(".gridRow:first")};a.pkp.controllers.grid.GridHandler.prototype.getElementsByType=function(b){return b.hasClass("gridRow")?(b=b.parents("tbody:first"),a(".gridRow",
b)):null};a.pkp.controllers.grid.GridHandler.prototype.getEmptyElement=function(b){return b.hasClass("gridRow")?b.parents("tbody:first").next(".empty"):null};a.pkp.controllers.grid.GridHandler.prototype.toggleRowActions=function(b,c){c.preventDefault();a(b).toggleClass("show_extras");a(b).toggleClass("hide_extras");b=a(b).parents("tr").next(".row_controls");this.applyToggleRowActionEffect_(b)};a.pkp.controllers.grid.GridHandler.prototype.hideAllVisibleRowActions=function(){this.getHtmlElement().find("a.hide_extras").click()};
a.pkp.controllers.grid.GridHandler.prototype.hideRowActionsDiv=function(){var b;var c=a(".gridRow div.row_actions",this.getHtmlElement());c.hide();var d=0;for(b=c.length;d<b;d++)a(c[d])};a.pkp.controllers.grid.GridHandler.prototype.showRowActionsDiv=function(){a(".gridRow div.row_actions",this.getHtmlElement()).show()};a.pkp.controllers.grid.GridHandler.prototype.changeLinkActionsState=function(b,c){void 0===c&&(c=a(".pkp_controllers_linkAction",this.getHtmlElement()));c.each(function(){var d=a.pkp.classes.Handler.getHandler(a(this));
b?d.enableLink():d.disableLink()})};a.pkp.controllers.grid.GridHandler.prototype.resequenceRows=function(b){var c;if(b){for(c in b){var d=b[c];var e=a("#"+a.pkp.classes.Helper.escapeJQuerySelector(String(d)));0==e.length&&(e=this.getRowByDataId(d));if(0==e.length)throw Error("Row with id "+d+" not found!");this.addElement(e)}this.updateControlRowsPosition();this.callFeaturesHook("resequenceRows",b)}};a.pkp.controllers.grid.GridHandler.prototype.updateControlRowsPosition=function(){var b;var c=this.getRows();
var d=0;for(b=c.length;d<b;d++){var e=a(c[d]);var f=this.getControlRowByGridRow(e);0<f.length&&f.insertAfter(e)}};a.pkp.controllers.grid.GridHandler.prototype.insertOrReplaceElement=function(b,c){b=a(b);var d=b.attr("id");var e=this.getHtmlElement();d=d?e.find("#"+a.pkp.classes.Helper.escapeJQuerySelector(d)):null;if(null!==d&&1<d.length)throw Error("There were "+d.length+" rather than 0 or 1 elements to be replaced!");this.hasSameNumOfColumns(b)?(null!==d&&1===d.length?this.replaceElement(d,b):this.addElement(b,
null,c),this.activateRowActions_()):a.get(this.fetchGridUrl_,null,this.callbackWrapper(this.replaceGridResponseHandler_),"json")};a.pkp.controllers.grid.GridHandler.prototype.deleteElement=function(b,c){if(1!==b.length)throw Error("There were "+b.length+" rather than 1 element to delete!");b.addClass("gridRowDeleted");var d=!1;1==this.getElementsByType(b).length&&(d=!0);b.hasClass("gridRow")&&this.deleteControlsRow_(b);var e=this.getEmptyElement(b);var f=this;var k=function(){f.unbindPartial(b);b.remove();
d&&e.fadeIn(100)};void 0!=c&&c?k():b.fadeOut(500,k)};a.pkp.controllers.grid.GridHandler.prototype.initialize=function(b){this.bind("dataChanged",this.refreshGridHandler);this.bind("addRow",this.addRowHandler_);this.bind("formSubmitted",this.refreshGridWithFilterHandler_);this.gridId_=b.gridId;this.fetchRowUrl=b.fetchRowUrl;this.fetchRowsUrl=b.fetchRowsUrl;this.fetchGridUrl_=b.fetchGridUrl;0<a("div.scrollable",this.getHtmlElement()).length?this.bodySelector="div.scrollable table":this.bodySelector=
b.bodySelector;this.activateRowActions_();this.setFetchExtraParams({});this.getHtmlElement().find(".pkp_form").hide();var c=this.getHtmlElement().find(".pkp_linkaction_search");0!==c.length?c.click(this.callbackWrapper(function(){this.getHtmlElement().find(".pkp_form").toggle();c.toggleClass("is_open")})):this.getHtmlElement().find(".pkp_form").toggle();this.trigger("gridInitialized")};a.pkp.controllers.grid.GridHandler.prototype.callFeaturesHook=function(b,c){var d;a.isArray(c)||(c=[c]);for(d in this.features_)this.features_[d][b].apply(this.features_[d],
c)};a.pkp.controllers.grid.GridHandler.prototype.refreshGridHandler=function(b,c,d,e){this.callFeaturesHook("refreshGrid",d);b=this.getFetchExtraParams();e||(d?d==a.pkp.controllers.grid.GridHandler.FETCH_ALL_ROWS_ID?a.get(this.fetchRowsUrl,b,this.callbackWrapper(this.replaceElementResponseHandler),"json"):(b.rowId=d,a.get(this.fetchRowUrl,b,this.callbackWrapper(this.replaceElementResponseHandler),"json")):a.get(this.fetchGridUrl_,b,this.callbackWrapper(this.replaceGridResponseHandler_),"json"));this.trigger("gridRefreshRequested");
this.publishChangeEvents()};a.pkp.controllers.grid.GridHandler.prototype.addElement=function(b,c,d){if(void 0===c||null===c)c=this.getHtmlElement().find(this.bodySelector);void 0!=d&&d?c.prepend(b):c.append(b);(c=this.getEmptyElement(b))&&c.hide();this.callFeaturesHook("addElement",b)};a.pkp.controllers.grid.GridHandler.prototype.replaceElement=function(b,c){c.hasClass("gridRow")&&this.deleteControlsRow_(b);this.replacePartialWith(c,b);this.callFeaturesHook("replaceElement",c)};a.pkp.controllers.grid.GridHandler.prototype.hasSameNumOfColumns=
function(b,c){var d=this.getHtmlElement().find("th").length;b=b.first().find("td");c=c?b.attr("colspan"):b.length;return d==c};a.pkp.controllers.grid.GridHandler.prototype.replaceElementResponseHandler=function(b,c){var d;b=this.handleJson(c);if(!1!==b)if(b.elementNotFound)c=b.elementNotFound,c=this.getRowByDataId(c),0<c.length&&this.deleteElement(c);else{var e=a(b.content);if(1<e.filter("tr:not('.row_controls')").length){c=e.filter("tr.gridRow");var f=e.filter("tr.row_controls");var k=0;for(d=c.length;k<
d;k++){e=a(c[k]);var g=this.getControlRowByGridRow(e,f);this.insertOrReplaceElement(e.add(g))}}else this.insertOrReplaceElement(b.content);this.resequenceRows(b.sequenceMap)}this.callFeaturesHook("replaceElementResponseHandler",b)};a.pkp.controllers.grid.GridHandler.prototype.refreshGridWithFilterHandler_=function(b,c,d){a.post(this.fetchGridUrl_,d,this.callbackWrapper(this.replaceGridResponseHandler_),"json")};a.pkp.controllers.grid.GridHandler.prototype.addRowHandler_=function(b,c,d){a.get(this.fetchRowUrl,
d,this.callbackWrapper(this.replaceElementResponseHandler),"json")};a.pkp.controllers.grid.GridHandler.prototype.replaceGridResponseHandler_=function(b,c){b=this.handleJson(c);if(!1!==b){var d=this.getHtmlElement();c=d.parent();d=d.find(".filter").is(":visible");this.replaceWith(b.content);b=a('div[id^="'+this.getGridIdPrefix()+'"]',c);this.setHtmlElement(b);this.activateRowActions_();d&&b.find(".pkp_linkaction_search").click()}};a.pkp.controllers.grid.GridHandler.prototype.deleteControlsRow_=function(b){b=
a("#"+a.pkp.classes.Helper.escapeJQuerySelector(b.attr("id"))+"-control-row",this.getHtmlElement());b.is("tr")&&b.hasClass("row_controls")&&(this.unbindPartial(b),b.remove())};a.pkp.controllers.grid.GridHandler.prototype.getControlRowByGridRow=function(b,c){c=void 0===c||null===c?this.getHtmlElement().find("tr"):c;b=b.attr("id");return c.filter("#"+a.pkp.classes.Helper.escapeJQuerySelector(b+"-control-row"))};a.pkp.controllers.grid.GridHandler.prototype.activateRowActions_=function(){var b=this.getHtmlElement();
this.getHtmlElement().find("tr.gridRow").not(".category");b.find("a.show_extras").unbind("click").bind("click",this.callbackWrapper(this.toggleRowActions))};a.pkp.controllers.grid.GridHandler.prototype.applyToggleRowActionEffect_=function(b){b.prev().find("td:not(.indent_row)").add(b.prev());b.toggle()};a.pkp.controllers.grid.GridHandler.prototype.addFeature_=function(b,c){this.features_||(this.features_=[]);this.features_[b]=c};a.pkp.controllers.grid.GridHandler.prototype.initFeatures_=function(b){var c;
for(c in b){var d=b[c].JSClass;null!==d&&(d=a.pkp.classes.Helper.objectFactory(d,[this,b[c].options]),this.addFeature_(c,d),this.features_[c].init())}}})(jQuery);(function(a){a.pkp.controllers.grid.CategoryGridHandler=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.CategoryGridHandler,a.pkp.controllers.grid.GridHandler);a.pkp.controllers.grid.CategoryGridHandler.prototype.getCategoryIdPrefix=function(){return this.getGridIdPrefix()+"-category-"};a.pkp.controllers.grid.CategoryGridHandler.prototype.getCategories=function(){return a(".category_grid_body:not(.empty)",this.getHtmlElement())};a.pkp.controllers.grid.CategoryGridHandler.prototype.getCategoryByDataId=
function(b){return a("#"+this.getCategoryIdPrefix()+b)};a.pkp.controllers.grid.CategoryGridHandler.prototype.getCategoryRow=function(b){var c=this.getHtmlElement();void 0!==b&&(c=b);return a("tr.category",c)};a.pkp.controllers.grid.CategoryGridHandler.prototype.getRowsInCategory=function(b){return a("tr.gridRow",b).not(".category")};a.pkp.controllers.grid.CategoryGridHandler.prototype.getCategoryEmptyPlaceholder=function(b){b="#"+b.attr("id")+"-emptyPlaceholder";return a(b,this.getHtmlElement())};
a.pkp.controllers.grid.CategoryGridHandler.prototype.getCategoryDataId=function(b){b=b.attr("id");var c=this.getCategoryIdPrefix().length;return b.slice(c)};a.pkp.controllers.grid.CategoryGridHandler.prototype.getCategoryDataIdByRowId=function(b){b=b.replace(this.getCategoryIdPrefix()," ");b=b.match("(.*)-row");return a.trim(b[1])};a.pkp.controllers.grid.CategoryGridHandler.prototype.getRowIdPrefix=function(){return this.getGridIdPrefix()+"-category-"};a.pkp.controllers.grid.CategoryGridHandler.prototype.getRowByDataId=
function(b){this.parent("getRowByDataId",b);return a("#"+this.getRowIdPrefix()+this.currentCategoryId_+"-row-"+b,this.getHtmlElement())};a.pkp.controllers.grid.CategoryGridHandler.prototype.getRowDataId=function(b){b=b.attr("id").slice(this.getRowIdPrefix().length);b=b.match("-row-(.*)");return a.trim(b[1])};a.pkp.controllers.grid.CategoryGridHandler.prototype.appendCategory=function(b){this.getHtmlElement().find(this.bodySelector).append(b)};a.pkp.controllers.grid.CategoryGridHandler.prototype.resequenceCategories=
function(b){var c;for(c in b){var d=b[c];d=a("#"+d);this.appendCategory(d)}this.updateEmptyPlaceholderPosition()};a.pkp.controllers.grid.CategoryGridHandler.prototype.updateEmptyPlaceholderPosition=function(){var b=this.getCategories(),c;var d=0;for(c=b.length;d<c;d++){var e=a(b[d]);var f=this.getCategoryEmptyPlaceholder(e);0<f.length&&f.insertAfter(e)}};a.pkp.controllers.grid.CategoryGridHandler.prototype.initialize=function(b){this.fetchCategoryUrl_=b.fetchCategoryUrl;this.parent("initialize",b)};
a.pkp.controllers.grid.CategoryGridHandler.prototype.getElementsByType=function(b){return b.hasClass("category_grid_body")?this.getCategories():this.parent("getElementsByType",b)};a.pkp.controllers.grid.CategoryGridHandler.prototype.getEmptyElement=function(b){return b.hasClass("category_grid_body")?this.getHtmlElement().find(".empty").not(".category_placeholder"):this.parent("getEmptyElement",b)};a.pkp.controllers.grid.CategoryGridHandler.prototype.refreshGridHandler=function(b,c,d){var e=!1;void 0!==
d&&(void 0!==d.parentElementId?(e={rowId:d[0],rowCategoryId:d.parentElementId},this.currentCategoryId_=d.parentElementId,a.get(this.fetchRowUrl,e,this.callbackWrapper(this.replaceElementResponseHandler),"json")):a.get(this.fetchCategoryUrl_,{rowId:d},this.callbackWrapper(this.replaceElementResponseHandler),"json"),e=!0);this.parent("refreshGridHandler",b,c,d,e)};a.pkp.controllers.grid.CategoryGridHandler.prototype.deleteElement=function(b){var c;if(1<b.length&&(2==b.length&&b.hasClass("category_grid_body")&&
b.hasClass("category")&&(b=b.filter(".category_grid_body")),this.currentCategoryId_)){var d=this.getCategoryByDataId(this.currentCategoryId_);var e=0;for(c=b.length;e<c;e++){var f=a(b[e]).parents("#"+d.attr("id"));if(1===f.length){b=a(b[e]);break}}}b.hasClass("category_grid_body")&&(d=this.getCategoryEmptyPlaceholder(b),this.unbindPartial(d),d.remove());this.parent("deleteElement",b)};a.pkp.controllers.grid.CategoryGridHandler.prototype.addElement=function(b){var c=null;b.hasClass("gridRow")&&(c=
this.getCategoryDataIdByRowId(b.attr("id")),c=this.getCategoryByDataId(c));this.parent("addElement",b,c);b.hasClass("category_grid_body")&&(b=this.getEmptyElement(b),this.getHtmlElement().find(this.bodySelector).append(b))};a.pkp.controllers.grid.CategoryGridHandler.prototype.replaceElement=function(b,c){if(c.hasClass("category_grid_body")){var d=this.getCategoryEmptyPlaceholder(b);this.unbindPartial(d);d.remove()}this.parent("replaceElement",b,c)};a.pkp.controllers.grid.CategoryGridHandler.prototype.hasSameNumOfColumns=
function(b){var c=b,d=!1;b.hasClass("category_grid_body")&&(c=b.find("tr"),d=!0);return this.parent("hasSameNumOfColumns",c,d)}})(jQuery);(function(a){a.pkp.controllers.grid.files=a.pkp.controllers.grid.files||{review:{}};a.pkp.controllers.grid.files.review.AuthorReviewRevisionsGridHandler=function(b,c){this.parent(b,c);this.bindGlobal("refreshRevisionsGrid",function(){this.refreshGridHandler()})};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.files.review.AuthorReviewRevisionsGridHandler,a.pkp.controllers.grid.GridHandler)})(jQuery);(function(a){a.pkp.controllers.listbuilder=a.pkp.controllers.listbuilder||{};a.pkp.controllers.listbuilder.ListbuilderHandler=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.controllers.listbuilder.ListbuilderHandler,a.pkp.controllers.grid.GridHandler);a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.sourceType_=null;a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.saveUrl_=null;a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.saveFieldName_=null;
a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.fetchOptionsUrl_=null;a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.editItemCallingContext_=null;a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.availableOptions_=!1;a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.initialize=function(b){this.parent("initialize",b);this.sourceType_=b.sourceType;this.saveUrl_=b.saveUrl;this.saveFieldName_=b.saveFieldName;this.fetchOptionsUrl_=b.fetchOptionsUrl;this.availableOptions_=
b.availableOptions;b=this.getHtmlElement();b.find(".actions .pkp_linkaction_addItem").mousedown(this.callbackWrapper(this.addItemHandler_));this.attachContentHandlers_(b);this.bind("formSubmitRequested",this.formSubmitHandler_);this.bind("formSubmitted",this.formSubmittedHandler_)};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.getSaveUrl_=function(){return this.saveUrl_};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.getSaveFieldName_=function(){return this.saveFieldName_};
a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.closeEdits=function(){var b=this.getHtmlElement().find(".gridRowEdit:visible");0!==b.length&&(this.saveRow(b),b.removeClass("gridRowEdit"))};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.save=function(){var b=this.getHtmlElement().find("input.deletions").val(),c=[],d=this;this.getHtmlElement().find('.gridRow input.isModified[value="1"]').each(function(k,g){k=a(g).parents(".gridRow");k=d.buildParamsFromInputs_(k.find(":input"));
c.push(k)});var e=this.getRows().length;b=JSON.stringify({deletions:b,changes:c,numberOfRows:e});if(e=this.getSaveUrl_())a.post(e,{data:b},this.callbackWrapper(this.saveResponseHandler_,null),"json");else{var f=this.getSaveFieldName_();e=this.getHtmlElement().find(":input[type=hidden]").filter(function(){return a(this).attr("name")==f}).first();0===e.length&&(e=a('<input type="hidden" />'),e.attr("name",f),this.getHtmlElement().append(e));e.attr("value",b)}};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.saveRow=
function(b){b.addClass("saveRowResponsePlaceholder");b=this.buildParamsFromInputs_(b.find(":input"));b.modify=!0;this.disableControls();a.ajax({url:this.getFetchRowUrl(),data:b,success:this.callbackWrapper(this.saveRowResponseHandler_,null),dataType:"json",async:!1})};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.getEmptyElement=function(b){return this.getHtmlElement().find(".empty")};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.addItemHandler_=function(b,c){this.availableOptions_&&
setTimeout(this.callbackWrapper(function(){this.closeEdits();this.disableControls();a.get(this.getFetchRowUrl(),{modify:!0},this.callbackWrapper(this.appendRowResponseHandler_,null),"json")}),0);return!1};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.deleteItemHandler_=function(b,c){this.closeEdits();c=a(b);b=c.closest(".gridRow");c=c.closest(".pkp_controllers_listbuilder").find(".deletions");var d=b.find('input[name="rowId"]').val();void 0!==d&&(c.val(c.val()+" "+d),this.getHtmlElement().trigger("formChange"));
this.deleteElement(b);this.availableOptions_=!0;return!1};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.appendRowResponseHandler_=function(b,c){b=this.handleJson(c);!1!==b&&(b=a(b.content),this.getHtmlElement().find(".empty").hide().before(b),this.attachContentHandlers_(b),b.addClass("gridRowEdit"),b.find(":input").not('[type="hidden"]').first().focus(),this.sourceType_==a.pkp.cons.LISTBUILDER_SOURCE_TYPE_SELECT?(this.disableControls(),a.get(this.fetchOptionsUrl_,{},this.callbackWrapper(this.fetchOptionsResponseHandler_,
null),"json")):this.enableControls(),this.callFeaturesHook("addElement",b));return!1};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.fetchOptionsResponseHandler_=function(b,c){b=this.handleJson(c);c=this.getHtmlElement();var d=[],e,f,k;if(!1!==b){c.find(".gridCellDisplay :input").each(function(G,K){d[G]=a(K).val()});c=c.find(".gridRowEdit:visible .selectMenu:input");var g=0;for(e=c.length;g<e;g++){var n=a(c[g]);var v=n.parents(".gridCellContainer");var t=0;n.children().empty();var q=null;
for(q in b.content[g])if(q!=a.pkp.cons.LISTBUILDER_OPTGROUP_LABEL)if("object"==typeof b.content[g][q]){if(void 0!==b.content[g][a.pkp.cons.LISTBUILDER_OPTGROUP_LABEL]&&"object"==typeof b.content[g][a.pkp.cons.LISTBUILDER_OPTGROUP_LABEL]&&(k=b.content[g][a.pkp.cons.LISTBUILDER_OPTGROUP_LABEL][q])){var A=a("<optgroup></optgroup>");A.attr("label",k);n.append(A);k=null;var B=0;for(k in b.content[g][q])if(f=this.populatePulldown_(A,d,b.content[g][q][k],k))t++,B++;0===B&&A.remove()}}else(f=this.populatePulldown_(n,
d,b.content[g][q],q))&&t++;n=f;1===t&&n&&(n.attr("selected","selected"),this.availableOptions_=!1);0===t&&(v.find(".gridCellDisplay").show(),v.find(".gridCellEdit").hide())}}this.enableControls();return!1};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.populatePulldown_=function(b,c,d,e){var f=b.parents(".gridCellContainer").find(".gridCellDisplay :input").val(),k=!1,g;if(e!=f)for(g=0;g<c.length;g++)c[g]==e&&(k=!0);if(k)return!1;c=a("<option/>");c.attr("value",e);c.text(d);e==f&&c.attr("selected",
"selected");b.append(c);return c};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.editItemHandler_=function(b,c){this.closeEdits();this.editItemCallingContext_=b;setTimeout(this.callbackWrapper(function(){var d=a(this.editItemCallingContext_).closest(".gridRow");d.addClass("gridRowEdit");d.find(":input").not('[type="hidden"]').first().focus();this.sourceType_==a.pkp.cons.LISTBUILDER_SOURCE_TYPE_SELECT&&(this.disableControls(),a.get(this.fetchOptionsUrl_,{},this.callbackWrapper(this.fetchOptionsResponseHandler_,
null),"json"))}),0);return!1};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.buildParamsFromInputs_=function(b){var c={};a.each(b.serializeArray(),function(d,e){d=e.name;e=e.value;c[d]=void 0===c[d]?e:a.isArray(c[d])?c[d].concat(e):[c[d],e]});return c};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.inputKeystrokeHandler_=function(b,c){if(13==c.which){var d=a(b);b=d.parents(".gridRow");c=b.find(":input:visible");d=c.index(d);c.length==d+1?this.saveRow(b):c[d+1].focus();return!1}return!0};
a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.inputBlurHandler_=function(b,c){a(b).closest(".gridRow").addClass("editingRowPlaceholder");setTimeout(this.callbackWrapper(function(){var d=a(".editingRowPlaceholder"),e=!1;d.find(":input").each(function(f,k){k===document.activeElement&&(e=!0)});d.removeClass("editingRowPlaceholder");e||this.closeEdits()}),0);return!0};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.saveRowResponseHandler_=function(b,c){b=this.handleJson(c);!1!==
b&&(b=a(b.content),c=this.getHtmlElement().find(".saveRowResponsePlaceholder").attr("id"),this.getHtmlElement().find(".saveRowResponsePlaceholder").replaceWith(b),b.attr("id",c),this.attachContentHandlers_(b),this.callFeaturesHook("replaceElement",b));this.getHtmlElement().trigger("formChange");this.enableControls()};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.saveResponseHandler_=function(b,c){};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.attachContentHandlers_=function(b){b.find(".gridCellDisplay").click(this.callbackWrapper(this.editItemHandler_));
b.find(":input").keypress(this.callbackWrapper(this.inputKeystrokeHandler_)).blur(this.callbackWrapper(this.inputBlurHandler_));b.find(".pkp_linkaction_delete").click(this.callbackWrapper(this.deleteItemHandler_))};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.formSubmitHandler_=function(b,c){this.save();this.getHtmlElement().find(".gridRow :input").attr("disabled","disabled");return!0};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.formSubmittedHandler_=function(b,c){this.getHtmlElement().find(".gridRow :input").removeAttr("disabled")};
a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.disableControls=function(){this.getHtmlElement().find('span[class="options"] > a[id*="addItem"]').unbind("mousedown");this.getHtmlElement().find('span[class="options"] > a[id*="addItem"]').mousedown(function(){return!1});this.getHtmlElement().find(".h3").addClass("spinner")};a.pkp.controllers.listbuilder.ListbuilderHandler.prototype.enableControls=function(){this.getHtmlElement().find('span[class="options"] > a[id*="addItem"]').mousedown(this.callbackWrapper(this.addItemHandler_));
this.getHtmlElement().find(".h3").removeClass("spinner")}})(jQuery);(function(a){a.pkp.controllers.modal=a.pkp.controllers.modal||{};a.pkp.controllers.modal.ModalHandler=function(b,c){this.parent(b,c);if(!this.checkOptions(c))throw Error("Missing or invalid modal options!");c=a.extend(!0,{},c);this.options=this.mergeOptions(c);this.modalOpen(b);this.publishEvent("redirectRequested");this.publishEvent("dataChanged");this.publishEvent("updateHeader");this.publishEvent("gridRefreshRequested");this.bind("notifyUser",this.redirectNotifyUserEventHandler_);this.bindGlobal("form-success",
this.onFormSuccess_)};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.ModalHandler,a.pkp.classes.Handler);a.pkp.controllers.modal.ModalHandler.DEFAULT_OPTIONS_={autoOpen:!0,width:710,modal:!0,draggable:!1,resizable:!1,position:{my:"center",at:"center center-10%",of:window},canClose:!0,closeCallback:!1,closeCleanVueInstances:[]};a.pkp.controllers.modal.ModalHandler.options=null;a.pkp.controllers.modal.ModalHandler.prototype.checkOptions=function(b){return"object"===typeof b&&void 0===b.buttons};
a.pkp.controllers.modal.ModalHandler.prototype.mergeOptions=function(b){return a.extend(!0,{},this.self("DEFAULT_OPTIONS_"),b)};a.pkp.controllers.modal.ModalHandler.prototype.modalOpen=function(b){this.uniqueModalId="id"+Math.random().toString(16).slice(2);b.trigger("pkpModalOpen",[b])};a.pkp.controllers.modal.ModalHandler.prototype.modalClose=function(b,c){var d=this,e=this.getHtmlElement();e.find("form").first();this.trigger("pkpModalClose");this.dialogProps?pkp.eventBus.$emit("close-dialog-vue"):
pkp.eventBus.$emit("close-modal-vue",{modalId:this.uniqueModalId});setTimeout(function(){var f=d.options.closeCleanVueInstances,k;if(f.length)for(k=0;k<f.length;k++){var g=f[k];"undefined"!==typeof pkp.registry._instances[g]&&(g=pkp.registry._instances[g],g.unmount())}d.unbindPartial(e);e.empty();d.remove();"function"===typeof d.options.closeCallback&&d.options.closeCallback.call()},300);return!1};a.pkp.controllers.modal.ModalHandler.prototype.handleWrapperEvents=function(b,c){"click"==c.type&&b==
c.target?a.pkp.classes.Handler.getHandler(a(b)).modalClose():"keyup"==c.type&&27==c.which&&a.pkp.classes.Handler.getHandler(a(b)).modalClose()};a.pkp.controllers.modal.ModalHandler.prototype.redirectNotifyUserEventHandler_=function(b,c,d){a.pkp.classes.notification.NotificationHelper.redirectNotifyUserEvent(this,d)};a.pkp.controllers.modal.ModalHandler.prototype.onFormSuccess_=function(b,c){if(this.options.closeOnFormSuccessId&&this.options.closeOnFormSuccessId===c){var d=this;pkp.eventBus.$emit("close-modal-vue-soon",
{modalId:this.uniqueModalId});setTimeout(function(){d.modalClose()},1500)}}})(jQuery);(function(a){a.pkp.controllers.modal.ConfirmationModalHandler=function(b,c){this.dialogProps={title:c.title,message:c.dialogText,actions:[],closeLegacyHandler:this.callbackWrapper(this.modalClose),modalStyle:c.modalStyle};c.okButton&&this.dialogProps.actions.push({label:c.okButton,isWarnable:"negative"===c.modalStyle,callback:this.callbackWrapper(this.modalConfirm)});c.cancelButton&&this.dialogProps.actions.push({label:c.cancelButton,isWarnable:"negative"!==c.modalStyle,callback:this.callbackWrapper(this.modalClose)});
this.parent(b,c);this.callback_=c.callback||null;this.callbackArgs_=c.callbackArgs||null;b.find(".pkpModalConfirmButton").on("click",this.callbackWrapper(this.modalConfirm))};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.ConfirmationModalHandler,a.pkp.controllers.modal.ModalHandler);a.pkp.controllers.modal.ConfirmationModalHandler.prototype.callback_=null;a.pkp.controllers.modal.ConfirmationModalHandler.prototype.callbackArgs_=null;a.pkp.controllers.modal.ConfirmationModalHandler.prototype.checkOptions=
function(b){return this.parent("checkOptions",b)?"string"===typeof b.okButton&&(!1===b.cancelButton||"string"===typeof b.cancelButton)&&"string"===typeof b.dialogText:!1};a.pkp.controllers.modal.ConfirmationModalHandler.prototype.modalOpen=function(b){this.parent("modalOpen",b);pkp.eventBus.$emit("open-dialog-vue",{dialogProps:this.dialogProps})};a.pkp.controllers.modal.ConfirmationModalHandler.prototype.modalConfirm=function(b,c){this.modalClose(b);this.callback_&&this.callback_.call(null,this.callbackArgs_)}})(jQuery);(function(a){a.pkp.controllers.modal.RedirectConfirmationModalHandler=function(b,c){this.parent(b,c);this.remoteUrl_=c.remoteUrl};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.RedirectConfirmationModalHandler,a.pkp.controllers.modal.ConfirmationModalHandler);a.pkp.controllers.modal.RedirectConfirmationModalHandler.prototype.remoteUrl_=null;a.pkp.controllers.modal.RedirectConfirmationModalHandler.prototype.checkOptions=function(b){return this.parent("checkOptions",b)?"string"===typeof b.cancelButton&&
"string"===typeof b.remoteUrl:!1};a.pkp.controllers.modal.RedirectConfirmationModalHandler.prototype.modalConfirm=function(b,c){document.location=this.remoteUrl_}})(jQuery);(function(a){a.pkp.controllers.modal.RemoteActionConfirmationModalHandler=function(b,c){this.parent(b,c);this.remoteAction_=c.remoteAction;this.postData_=c.postData||{};this.postData_.csrfToken=c.csrfToken};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.RemoteActionConfirmationModalHandler,a.pkp.controllers.modal.ConfirmationModalHandler);a.pkp.controllers.modal.RemoteActionConfirmationModalHandler.prototype.remoteAction_=null;a.pkp.controllers.modal.RemoteActionConfirmationModalHandler.prototype.postData_=
null;a.pkp.controllers.modal.RemoteActionConfirmationModalHandler.prototype.checkOptions=function(b){return this.parent("checkOptions",b)?"string"===typeof b.cancelButton&&"string"===typeof b.remoteAction:!1};a.pkp.controllers.modal.RemoteActionConfirmationModalHandler.prototype.modalConfirm=function(b,c){a.post(this.remoteAction_,this.postData_,this.callbackWrapper(this.remoteResponse),"json")};a.pkp.controllers.modal.RemoteActionConfirmationModalHandler.prototype.remoteResponse=function(b,c){!1!==
this.parent("remoteResponse",b,c)&&this.modalClose(b);return!1}})(jQuery);(function(a){a.pkp.controllers.modal.ButtonConfirmationModalHandler=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.ButtonConfirmationModalHandler,a.pkp.controllers.modal.ConfirmationModalHandler);a.pkp.controllers.modal.ButtonConfirmationModalHandler.prototype.checkOptions=function(b){return this.parent("checkOptions",b)?"object"==typeof b.$button&&1==b.$button.length:!1};a.pkp.controllers.modal.ButtonConfirmationModalHandler.prototype.modalConfirm=function(b,
c){c=this.options.$button;this.modalClose(b);"submit"==c.attr("type")?c.trigger("submit"):c.click()}})(jQuery);(function(a){a.pkp.controllers.modal.JsEventConfirmationModalHandler=function(b,c){this.parent(b,c);this.jsEvent_=c.jsEvent;this.extraArguments_=c.extraArguments};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.JsEventConfirmationModalHandler,a.pkp.controllers.modal.ConfirmationModalHandler);a.pkp.controllers.modal.JsEventConfirmationModalHandler.prototype.jsEvent_=null;a.pkp.controllers.modal.JsEventConfirmationModalHandler.prototype.extraArguments_=null;a.pkp.controllers.modal.JsEventConfirmationModalHandler.prototype.checkOptions=
function(b){return this.parent("checkOptions",b)?"string"===typeof b.cancelButton&&"string"===typeof b.jsEvent:!1};a.pkp.controllers.modal.JsEventConfirmationModalHandler.prototype.modalConfirm=function(b,c){this.trigger(this.jsEvent_,this.extraArguments_);this.modalClose(b)}})(jQuery);(function(a){a.pkp.controllers.modal.AjaxModalHandler=function(b,c){this.parent(b,c);this.bind("formSubmitted",this.formSubmitted);this.bind("formCanceled",this.modalClose);this.bind("ajaxHtmlError",this.modalClose);this.bind("modalFinished",this.modalClose)};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.AjaxModalHandler,a.pkp.controllers.modal.ModalHandler);a.pkp.controllers.modal.AjaxModalHandler.prototype.checkOptions=function(b){return this.parent("checkOptions",b)?"string"===typeof b.url:
!1};a.pkp.controllers.modal.AjaxModalHandler.prototype.mergeOptions=function(b){return this.parent("mergeOptions",b)};a.pkp.controllers.modal.AjaxModalHandler.prototype.modalOpen=function(b){this.parent("modalOpen",b);pkp.eventBus.$emit("open-modal-vue",{component:"LegacyAjax",modalId:this.uniqueModalId,options:Object.assign({},this.options,{modalHandler:this})})};a.pkp.controllers.modal.AjaxModalHandler.prototype.formSubmitted=function(b,c){this.getHtmlElement().parent().trigger("notifyUser");this.modalClose()}})(jQuery);(function(a){a.pkp.controllers.modal.VueModalHandler=function(b,c){this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.VueModalHandler,a.pkp.controllers.modal.ModalHandler);a.pkp.controllers.modal.VueModalHandler.prototype.checkOptions=function(b){return this.parent("checkOptions",b)?"string"===typeof b.component:!1};a.pkp.controllers.modal.VueModalHandler.prototype.mergeOptions=function(b){return this.parent("mergeOptions",b)};a.pkp.controllers.modal.VueModalHandler.prototype.modalOpen=
function(b){this.parent("modalOpen",b);pkp.eventBus.$emit("open-modal-vue",{component:this.options.component,modalId:this.uniqueModalId,options:Object.assign({},this.options,{modalHandler:this})})}})(jQuery);(function(a){a.pkp.controllers.modal.WizardModalHandler=function(b,c){this.parent(b,c);this.bind("wizardClose",this.wizardClose);this.bind("wizardCancel",this.wizardClose)};a.pkp.classes.Helper.inherits(a.pkp.controllers.modal.WizardModalHandler,a.pkp.controllers.modal.AjaxModalHandler);a.pkp.controllers.modal.WizardModalHandler.prototype.modalClose=function(b,c,d){d?this.parent("modalClose",b,c):(d=new a.Event("wizardCancelRequested"),d.stopPropagation(),this.getHtmlElement().children().first().trigger(d),
d.isDefaultPrevented()||this.parent("modalClose",b,c));return!1};a.pkp.controllers.modal.WizardModalHandler.prototype.wizardClose=function(b,c){this.modalClose(b,c,!0)}})(jQuery);(function(a){jQuery.pkp.controllers.linkAction=jQuery.pkp.controllers.linkAction||{};a.pkp.controllers.linkAction.LinkActionHandler=function(b,c){this.parent(b,c);this.staticId_=c.staticId?c.staticId:b.attr("id");if(!c.actionRequest||!c.actionRequestOptions)throw Error('The "actionRequest" and "actionRequestOptions"settings are required in a LinkActionHandler');c.actionRequestOptions.finishCallback=this.callbackWrapper(this.enableLink);this.linkActionRequest_=a.pkp.classes.Helper.objectFactory(c.actionRequest,
[b,c.actionRequestOptions]);this.bindActionRequest();this.publishEvent("dataChanged");this.bind("dataChanged",this.dataChangedHandler_);this.bind("pkpModalClose",this.removeDisabledAttribute_);c.selfActivate&&this.trigger("click")};a.pkp.classes.Helper.inherits(a.pkp.controllers.linkAction.LinkActionHandler,a.pkp.classes.Handler);a.pkp.controllers.linkAction.LinkActionHandler.prototype.linkActionRequest_=null;a.pkp.controllers.linkAction.LinkActionHandler.prototype.staticId_=null;a.pkp.controllers.linkAction.LinkActionHandler.prototype.getStaticId=
function(){return this.staticId_};a.pkp.controllers.linkAction.LinkActionHandler.prototype.getUrl=function(){return this.linkActionRequest_.getUrl()};a.pkp.controllers.linkAction.LinkActionHandler.prototype.activateAction=function(b,c){this.linkActionRequest_.shouldDebounce()&&this.disableLink();return this.linkActionRequest_.activate.call(this.linkActionRequest_,b,c)};a.pkp.controllers.linkAction.LinkActionHandler.prototype.bindActionRequest=function(){this.bind("click",this.activateAction)};a.pkp.controllers.linkAction.LinkActionHandler.prototype.enableLink=
function(){var b=a(this.getHtmlElement());this.getHtmlElement().is(":submit")||this.removeDisabledAttribute_();var c=this.getUrl();this.getHtmlElement().is("a")&&c&&b.attr("href",c);this.unbind("click",this.noAction_);this.bindActionRequest()};a.pkp.controllers.linkAction.LinkActionHandler.prototype.disableLink=function(){var b=a(this.getHtmlElement());b.attr("disabled","disabled");this.getHtmlElement().is("a")&&b.attr("href","#");this.unbind("click",this.activateAction);this.bind("click",this.noAction_)};
a.pkp.controllers.linkAction.LinkActionHandler.prototype.removeDisabledAttribute_=function(){a(this.getHtmlElement()).removeAttr("disabled")};a.pkp.controllers.linkAction.LinkActionHandler.prototype.dataChangedHandler_=function(b,c,d){0===this.getHtmlElement().parents(".pkp_controllers_grid").length&&this.trigger("redirectDataChangedToGrid",[d]);this.trigger("notifyUser")};a.pkp.controllers.linkAction.LinkActionHandler.prototype.noAction_=function(){return!1}})(jQuery);(function(a){a.pkp.controllers.wizard=a.pkp.controllers.wizard||{};a.pkp.controllers.wizard.WizardHandler=function(b,c){this.parent(b,c);this.addWizardButtons_(b,c);this.enforceLinear_=c.hasOwnProperty("enforceLinear")?c.enforceLinear:!0;this.startWizard();this.bindWizardEvents();this.bind("formValid",this.formValid);this.bind("formInvalid",this.formInvalid);this.bind("formSubmitted",this.formSubmitted)};a.pkp.classes.Helper.inherits(a.pkp.controllers.wizard.WizardHandler,a.pkp.controllers.TabHandler);
a.pkp.controllers.wizard.WizardHandler.prototype.$continueButton_=null;a.pkp.controllers.wizard.WizardHandler.prototype.$progressIndicator_=null;a.pkp.controllers.wizard.WizardHandler.prototype.continueButtonText_=null;a.pkp.controllers.wizard.WizardHandler.prototype.finishButtonText_=null;a.pkp.controllers.wizard.WizardHandler.prototype.enforceLinear_=null;a.pkp.controllers.wizard.WizardHandler.prototype.showProgressIndicator_=function(){this.getProgressIndicator().css("opacity",1)};a.pkp.controllers.wizard.WizardHandler.prototype.hideProgressIndicator_=
function(){this.getProgressIndicator().css("opacity",0)};a.pkp.controllers.wizard.WizardHandler.prototype.continueRequest=function(b,c){b=new a.Event("wizardAdvanceRequested");this.getCurrentTab().children().first().trigger(b);b.isDefaultPrevented()||this.advanceOrClose_();return!1};a.pkp.controllers.wizard.WizardHandler.prototype.formValid=function(b,c){this.enableContinueButton()};a.pkp.controllers.wizard.WizardHandler.prototype.formInvalid=function(b,c){this.disableContinueButton()};a.pkp.controllers.wizard.WizardHandler.prototype.formSubmitted=
function(b,c){this.advanceOrClose_()};a.pkp.controllers.wizard.WizardHandler.prototype.cancelRequest=function(b,c){this.checkForm_(!1);b=new a.Event("wizardCancelRequested");this.getCurrentTab().children().first().trigger(b);b.isDefaultPrevented()||this.trigger("wizardCancel");return!1};a.pkp.controllers.wizard.WizardHandler.prototype.wizardCancelRequested=function(b,c){return this.checkForm_(!0)?!1:!0};a.pkp.controllers.wizard.WizardHandler.prototype.wizardAdvanceRequested=function(b,c){if(b=this.getForm_())b.submit()&&
(this.disableContinueButton(),this.showProgressIndicator_()),c.preventDefault()};a.pkp.controllers.wizard.WizardHandler.prototype.wizardAdvance=function(b,c){var d=this.getCurrentStep();b=this.getNumberOfSteps()-1;c=d+1;if(c>b)throw Error("Trying to set an invalid wizard step!");var e=this.getHtmlElement();e.tabs("enable",c);e.tabs("option","active",c);this.enforceLinear_&&e.tabs("disable",d);d=this.getContinueButton();c===b&&d.text(this.getFinishButtonText());this.hideProgressIndicator_();this.enableContinueButton()};
a.pkp.controllers.wizard.WizardHandler.prototype.startWizard=function(){var b=this.getHtmlElement(),c;if(0!==this.getCurrentStep()){b.tabs("enable",0);b.tabs("option","active",0);var d=this.getContinueButton();d.text(this.getContinueButtonText())}if(this.enforceLinear_){d=[];for(c=1;c<this.getNumberOfSteps();c++)d.push(c);b.tabs("option","disabled",d)}};a.pkp.controllers.wizard.WizardHandler.prototype.bindWizardEvents=function(){this.bind("wizardCancelRequested",this.wizardCancelRequested);this.bind("wizardAdvanceRequested",
this.wizardAdvanceRequested);this.bind("wizardAdvance",this.wizardAdvance)};a.pkp.controllers.wizard.WizardHandler.prototype.getCurrentStep=function(){return this.getCurrentTabIndex()};a.pkp.controllers.wizard.WizardHandler.prototype.getContinueButton=function(){return this.$continueButton_};a.pkp.controllers.wizard.WizardHandler.prototype.getProgressIndicator=function(){return this.$progressIndicator_};a.pkp.controllers.wizard.WizardHandler.prototype.getContinueButtonText=function(){return this.continueButtonText_};
a.pkp.controllers.wizard.WizardHandler.prototype.getFinishButtonText=function(){return this.finishButtonText_};a.pkp.controllers.wizard.WizardHandler.prototype.getNumberOfSteps=function(){return this.getHtmlElement().find("ul").first().children().length};a.pkp.controllers.wizard.WizardHandler.prototype.getForm_=function(){var b;var c=this.getCurrentTab().children();for(b=0;b<c.length;b++){var d=a(c[b]);if(d.is("form"))return d}return null};a.pkp.controllers.wizard.WizardHandler.prototype.advanceOrClose_=
function(){var b=this.getCurrentStep(),c=this.getNumberOfSteps()-1;b<c?this.trigger("wizardAdvance"):this.trigger("wizardClose")};a.pkp.controllers.wizard.WizardHandler.prototype.checkForm_=function(b){var c=this.getForm_();if(null!==c)if(c=a.pkp.classes.Handler.getHandler(a("#"+c.attr("id"))),b){if(c.formChangesTracked)if(confirm(pkp.localeKeys["form.dataHasChanged"]))c.unregisterForm();else return!0}else c.unregisterForm();return!1};a.pkp.controllers.wizard.WizardHandler.prototype.addWizardButtons_=
function(b,c){var d=a('<div id="wizardButtons" class="modal_buttons"></div>');if(c.continueButtonText){var e=a('<button id="continueButton" class="pkp_button"></button>').text(c.continueButtonText);d.append(e);var f=a('<span class="pkp_spinner"></span>');d.append(f);e.bind("click",this.callbackWrapper(this.continueRequest));this.$continueButton_=e;this.$progressIndicator_=f;this.continueButtonText_=c.continueButtonText;this.finishButtonText_=c.finishButtonText?c.finishButtonText:c.continueButtonText}c.cancelButtonText&&
(c=a('<a id="cancelButton" class="cancel" href="#"></a>').text(c.cancelButtonText),d.append(c),c.bind("click",this.callbackWrapper(this.cancelRequest)));b.after(d)};a.pkp.controllers.wizard.WizardHandler.prototype.disableContinueButton=function(){this.getContinueButton().attr("disabled","disabled")};a.pkp.controllers.wizard.WizardHandler.prototype.enableContinueButton=function(){this.getContinueButton().removeAttr("disabled")}})(jQuery);(function(a){a.pkp.controllers.grid.queries=a.pkp.controllers.grid.queries||{};a.pkp.controllers.grid.queries.ReadQueryHandler=function(b,c){this.fetchNoteFormUrl_=c.fetchNoteFormUrl;this.fetchParticipantsListUrl_=c.fetchParticipantsListUrl;b.find(".openNoteForm a").click(this.callbackWrapper(this.showNoteFormHandler_));b.bind("dataChanged",this.callbackWrapper(this.reloadParticipantsList_));b.bind("user-left-discussion",function(){b.parent().trigger("modalFinished")});this.loadParticipantsList()};
a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.queries.ReadQueryHandler,a.pkp.classes.Handler);a.pkp.controllers.grid.queries.ReadQueryHandler.prototype.fetchNoteFormUrl_=null;a.pkp.controllers.grid.queries.ReadQueryHandler.prototype.fetchParticipantsListUrl_=null;a.pkp.controllers.grid.queries.ReadQueryHandler.prototype.loadParticipantsList=function(){a.get(this.fetchParticipantsListUrl_,this.callbackWrapper(this.showFetchedParticipantsList_),"json")};a.pkp.controllers.grid.queries.ReadQueryHandler.prototype.showNoteFormHandler_=
function(b){a(b).parents(".queryEditButtons").addClass("is_loading");a.get(this.fetchNoteFormUrl_,this.callbackWrapper(this.showFetchedNoteForm_),"json")};a.pkp.controllers.grid.queries.ReadQueryHandler.prototype.showFetchedNoteForm_=function(b,c){b=this.handleJson(c);c=a("#newNotePlaceholder",this.getHtmlElement());var d=a(".queryEditButtons.is_loading",this.getHtmlElement());this.unbindPartial(d);d.remove();this.unbindPartial(c);c.html(b.content)};a.pkp.controllers.grid.queries.ReadQueryHandler.prototype.showFetchedParticipantsList_=
function(b,c){b=this.handleJson(c);c=a("#participantsListPlaceholder",this.getHtmlElement());var d=a(".leaveQueryForm",this.getHtmlElement());b.showLeaveQueryButton?d.show():d.hide();this.unbindPartial(c);c.children().remove();c.append(b.content)};a.pkp.controllers.grid.queries.ReadQueryHandler.prototype.reloadParticipantsList_=function(b,c,d){this.loadParticipantsList()}})(jQuery);(function(a){a.pkp.controllers.grid.queries=a.pkp.controllers.grid.queries||{};a.pkp.controllers.grid.queries.QueryFormHandler=function(b,c){this.parent(b,c);c.templateUrl&&(this.templateUrl_=c.templateUrl);b.find("#template").change(this.callbackWrapper(this.selectTemplateHandler_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.queries.QueryFormHandler,a.pkp.controllers.form.CancelActionAjaxFormHandler);a.pkp.controllers.grid.queries.QueryFormHandler.prototype.templateUrl_=null;a.pkp.controllers.grid.queries.QueryFormHandler.prototype.selectTemplateHandler_=
function(b,c){b=this.getHtmlElement().find('[name="template"]');a.post(this.templateUrl_,b.serialize(),this.callbackWrapper(this.updateTemplate),"json")};a.pkp.controllers.grid.queries.QueryFormHandler.prototype.updateTemplate=function(b,c){b=this.getHtmlElement();var d=this.handleJson(c);c=c.content;var e=b.find('textarea[name="comment"]'),f=tinyMCE.EditorManager.get(e.attr("id"));c.variables&&e.attr("data-variables",JSON.stringify(c.variables));f.setContent(c.body);b.find('[name="subject"]').val(c.subject);
return d.status}})(jQuery);(function(a){a.pkp.controllers.wizard.fileUpload=a.pkp.controllers.wizard.fileUpload||{};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler=function(b,c){this.parent(b,c);this.csrfToken_=c.csrfToken;this.deleteUrl_=c.deleteUrl;this.metadataUrl_=c.metadataUrl;this.finishUrl_=c.finishUrl;this.cancelUrl_=c.cancelUrl;this.bind("fileUploaded",this.handleFileUploaded);this.bind("filesRemoved",this.handleRemovedFiles);this.disableContinueButton()};a.pkp.classes.Helper.inherits(a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler,
a.pkp.controllers.wizard.WizardHandler);a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.csrfToken_="";a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.deleteUrl_="";a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.metadataUrl_="";a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.finishUrl_="";a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.cancelUrl_="";a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.uploadedFile_=
null;a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.originalFile_=null;a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.tabsBeforeActivate=function(b,c,d){if(0<d.newTab.index()){if(!this.uploadedFile_)throw Error("Uploaded file missing!");this.getHtmlElement();switch(d.newTab.index()){case 1:var e=this.metadataUrl_;break;case 2:e=this.finishUrl_;break;default:throw Error("Unsupported tab index.");}e=e+"&submissionFileId="+this.uploadedFile_.id;d.newTab.find(".ui-tabs-anchor").attr("href",
e)}return this.parent("tabsBeforeActivate",b,c,d)};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.wizardAdvance=function(b,c){b=this.getCurrentStep();c=this.getNumberOfSteps()-1;var d=b+1,e=this.getHtmlElement();if(d>c)throw Error("Trying to set an invalid wizard step!");e.tabs("enable",d);e.tabs("option","active",d);0===b&&e.tabs("disable",b);d===c&&(b=this.getContinueButton(),b.text(this.getFinishButtonText()),this.enableContinueButton())};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.tabsLoad=
function(b,c,d){var e=this.getHtmlElement(),f=this.getProgressIndicator();2===d.tab.index()&&(e=a("#newFile",e),e.length&&e.bind("click",this.callbackWrapper(this.startWizard)));f.hide();return this.parent("tabsLoad",b,c,d)};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.formValid=function(b,c){(0!==this.getCurrentStep()||0!==this.getHtmlElement().find("#uploadConfirmationForm").length||this.uploadedFile_)&&this.parent("formValid",b,c)};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.wizardCancelRequested=
function(b,c){if(this.parent("wizardCancelRequested",b,c))if(this.uploadedFile_)this.uploadedFile_.csrfToken=this.csrfToken_,this.uploadedFile_.submissionFileId=this.uploadedFile_.id,this.uploadedFile_.originalFile=this.originalFile_,a.post(this.cancelUrl_,this.uploadedFile_,a.pkp.classes.Helper.curry(this.wizardCancelSuccess,this,b,c),"json"),this.uploadedFile_=null,c.preventDefault();else return!0;return!1};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.wizardCancelSuccess=
function(b,c,d){!1!==this.handleJson(d)&&this.trigger("wizardCancel")};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.handleFileUploaded=function(b,c,d){null===this.originalFile_&&(this.originalFile_=d.originalFile);delete d.originalFile;this.uploadedFile_=d};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.handleRemovedFiles=function(b,c,d,e){var f;if("undefined"!==typeof e&&e.length)for(f in e){if("undefined"===typeof e[f].storedData)break;e[f].storedData.csrfToken=
this.csrfToken_;a.post(this.deleteUrl_,e[f].storedData)}};a.pkp.controllers.wizard.fileUpload.FileUploadWizardHandler.prototype.startWizard=function(){this.uploadedFile_=this.originalFile_=null;this.parent("startWizard")}})(jQuery);(function(a){a.pkp.controllers.wizard.fileUpload.form=a.pkp.controllers.wizard.fileUpload.form||{};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler=function(b,c){this.parent(b,c);this.hasFileSelector_=c.hasFileSelector;this.hasGenreSelector_=c.hasGenreSelector;c.presetRevisedFileId&&(this.presetRevisedFileId_=c.presetRevisedFileId);this.fileGenres_=c.fileGenres;this.$uploader_=c.$uploader;this.attachUploader_(this.$uploader_,c.uploaderOptions);this.uploaderSetup(c.$uploader);this.$revisedFileSelector_=
b.find("#revisedFileId").change(this.callbackWrapper(this.revisedFileChange));this.hasGenreSelector_&&(this.$genreSelector=b.find("#genreId").change(this.callbackWrapper(this.genreChange)));this.setUploaderVisibility_()};a.pkp.classes.Helper.inherits(a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler,a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.hasFileSelector_=!1;a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.$revisedFileSelector_=
null;a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.hasGenreSelector_=!1;a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.$genreSelector_=null;a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.presetRevisedFileId_=null;a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.fileGenres_=null;a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.$uploader_=null;a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.uploaderSetup=
function(b){b=a.pkp.classes.Handler.getHandler(b);b.pluploader.bind("BeforeUpload",this.callbackWrapper(this.prepareFileUploadRequest));b.pluploader.bind("FileUploaded",this.callbackWrapper(this.handleUploadResponse));b.pluploader.bind("FilesRemoved",this.callbackWrapper(this.handleRemovedFiles))};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.prepareFileUploadRequest=function(b,c){this.getHtmlElement();b={};this.hasFileSelector_?(this.$revisedFileSelector_.attr("disabled",
"disabled"),b.revisedFileId=this.$revisedFileSelector_.val()):b.revisedFileId=null!==this.presetRevisedFileId_?this.presetRevisedFileId_:0;this.hasGenreSelector_?(this.$genreSelector.attr("disabled","disabled"),b.genreId=this.$genreSelector.val()):b.genreId="";c.settings.multipart_params=b};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.handleUploadResponse=function(b,c,d,e){b=this.handleJson(a.parseJSON(e.response));this.getHtmlElement();!1!==b&&(b.uploadedFile&&this.trigger("fileUploaded",
b.uploadedFile),""!==b.content&&this.replaceWith(b.content));this.getHtmlElement().valid()};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.handleRemovedFiles=function(b,c,d){this.trigger("filesRemoved",[c,d])};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.submitForm=function(b,c){this.trigger("formSubmitted")};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.revisedFileChange=function(b,c){this.$revisedFileSelector_.val()?
(this.$genreSelector.val(this.fileGenres_[this.$revisedFileSelector_.val()]),this.$genreSelector.attr("disabled","disabled")):this.$genreSelector.removeAttr("disabled");this.setUploaderVisibility_();return!1};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.genreChange=function(b,c){this.setUploaderVisibility_()};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.attachUploader_=function(b,c){b.pkpHandler("$.pkp.controllers.UploaderHandler",c)};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.setUploaderVisibility_=
function(){this.hasGenreSelector_&&this.$genreSelector.val()||this.$revisedFileSelector_.val()?this.showUploader_():this.hasGenreSelector_||this.hasFileSelector_?this.hideUploader_():this.showUploader_()};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.hideUploader_=function(){this.$uploader_.addClass("pkp_screen_reader")};a.pkp.controllers.wizard.fileUpload.form.FileUploadFormHandler.prototype.showUploader_=function(){this.$uploader_.removeClass("pkp_screen_reader");a.pkp.classes.Handler.getHandler(this.$uploader_).pluploader.refresh()}})(jQuery);(function(a){a.pkp.controllers.grid.navigationMenus=a.pkp.controllers.grid.navigationMenus||{form:{}};a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler=function(b,c){this.okButton_=c.okButton;this.warningModalTitle_=c.warningModalTitle;this.submenuWarning_=c.submenuWarning;this.itemTypeConditionalWarnings_=c.itemTypeConditionalWarnings;b.on("click",".btnConditionalDisplay",this.callbackWrapper(this.showConditionalDisplayWarning));b.on("click",".btnSubmenuWarning",this.callbackWrapper(this.showSubmenuWarning));
this.parent(b,c);this.initSorting()};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler,a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.okButton_=null;a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.warningModalTitle_=null;a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.submenuWarning_=void 0;a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.itemTypeConditionalWarnings_=
null;a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.initSorting=function(){var b=this;a(".btnSubmenuWarning",this.getHtmlElement()).remove();a("#pkpNavAssigned > li").each(function(){var c=a(this).children("ul"),d=c.children(),e=d.find("li");c.length?d.length?(e.length&&e.each(function(){a(this).appendTo(c)}),a(this).find("> .item > .item_buttons .btnSubmenuWarning").length||a(this).find("> .item > .item_buttons").prepend(a("<button></button>").addClass("btnSubmenuWarning").append(a("<span></span>").addClass("fa fa-exclamation-triangle")).append(a("<span></span>").addClass("-screenReader").text(b.submenuWarning_)))):
c.replaceWith("<ul></ul>"):a(this).append("<ul></ul>")});a("#pkpNavUnassigned > li").each(function(){var c=a(this).children("ul");c.length&&c.find("li").each(function(){a(this).appendTo(a("#pkpNavUnassigned"))});c.remove()});a("#pkpNavManagement ul").sortable({placeholder:"pkp_nav_item_placeholder",delay:250,connectWith:"#pkpNavManagement ul",update:this.callbackWrapper(this.updateSorting),start:function(){a("#pkpNavAssigned").addClass("pkp_is_sorting")},stop:function(){a("#pkpNavAssigned").removeClass("pkp_is_sorting")}})};
a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.updateSorting=function(){var b=a("#pkpNavManagement"),c=0,d="";this.initSorting();a("input",b).remove();a("#pkpNavAssigned > li").each(function(){d="menuTree["+a(this).data("id")+"]";b.append('<input type="hidden" name="'+d+'[seq]" value="'+c+'">');c++;var e=a(this).data("id");a(this).find("li").each(function(){d="menuTree["+a(this).data("id")+"]";b.append('<input type="hidden" name="'+d+'[seq]" value="'+c+'">');b.append('<input type="hidden" name="'+
d+'[parentId]" value="'+e+'">');c++})})};a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.showConditionalDisplayWarning=function(b){b=a(b).closest("li").data("type");var c={title:this.warningModalTitle_,okButton:this.okButton_,cancelButton:!1,dialogText:this.itemTypeConditionalWarnings_[b]};null!==this.itemTypeConditionalWarnings_[b]&&a('<div id="'+a.pkp.classes.Helper.uuid()+'" class="pkp_modal pkpModalWrapper" tabindex="-1"></div>').pkpHandler("$.pkp.controllers.modal.ConfirmationModalHandler",
c);return!1};a.pkp.controllers.grid.navigationMenus.form.NavigationMenuFormHandler.prototype.showSubmenuWarning=function(){var b={title:this.warningModalTitle_,okButton:this.okButton_,cancelButton:!1,dialogText:this.submenuWarning_};a('<div id="'+a.pkp.classes.Helper.uuid()+'" class="pkp_modal pkpModalWrapper" tabindex="-1"></div>').pkpHandler("$.pkp.controllers.modal.ConfirmationModalHandler",b);return!1}})(jQuery);(function(a){a.pkp.controllers.grid.navigationMenus.form.NavigationMenuItemsFormHandler=function(b,c){this.parent(b,c);this.previewUrl_=c.previewUrl;this.itemTypeDescriptions_=c.itemTypeDescriptions;this.itemTypeConditionalWarnings_=c.itemTypeConditionalWarnings;a("#previewButton",b).click(this.callbackWrapper(this.showPreview_));a("#menuItemType",b).change(this.callbackWrapper(this.setType));a("#menuItemType",b).trigger("change")};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.navigationMenus.form.NavigationMenuItemsFormHandler,
a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.grid.navigationMenus.form.NavigationMenuItemsFormHandler.prototype.previewUrl_=null;a.pkp.controllers.grid.navigationMenus.form.NavigationMenuItemsFormHandler.prototype.itemTypeDescriptions_=null;a.pkp.controllers.grid.navigationMenus.form.NavigationMenuItemsFormHandler.prototype.itemTypeConditionalWarnings_=null;a.pkp.controllers.grid.navigationMenus.form.NavigationMenuItemsFormHandler.prototype.showPreview_=function(b,c){b=this.getHtmlElement();
a.post(this.previewUrl_,b.serialize(),function(d){var e=window.open("about:blank");e.document.open();e.document.write(d);e.document.close()});return!0};a.pkp.controllers.grid.navigationMenus.form.NavigationMenuItemsFormHandler.prototype.setType=function(){var b=a("#menuItemType",this.getHtmlElement()).val(),c=a('#menuItemTypeSection [for="menuItemType"]');a(".NMI_TYPE_CUSTOM_EDIT",this.getHtmlElement()).hide();a("#"+b).fadeIn();"undefined"!==typeof this.itemTypeDescriptions_[b]&&c.text(this.itemTypeDescriptions_[b])}})(jQuery);(function(a){a.pkp.controllers.grid.representations=a.pkp.controllers.grid.representations||{form:{}};a.pkp.controllers.grid.representations.form.RepresentationFormHandler=function(b,c){this.parent(b,c);(this.remoteRepresentation_=c.remoteRepresentation)?(a("#remotelyHostedContent").prop("checked",!0),a("#remote").show(20),a("#urlPathSection").hide()):(a("#remotelyHostedContent").prop("checked",!1),a("#remote").hide(),a("#urlPathSection").show(20));a("#remotelyHostedContent").change(this.callbackWrapper(this.toggleRemote_))};
a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.representations.form.RepresentationFormHandler,a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.grid.representations.form.RepresentationFormHandler.prototype.toggleRemote_=function(b,c){a("#remotelyHostedContent").prop("checked")?(a("#remote").show(20),a("#urlPathSection").hide(),a('input[id^="urlPath"]').val("")):(a("#remote").hide(20),a('input[id^="urlRemote"]').val(""),a("#urlPathSection").show(20));return!0}})(jQuery);(function(a){a.pkp.controllers.grid.settings=a.pkp.controllers.grid.settings||{user:{form:{}}};a.pkp.controllers.grid.settings.user.form.UserDetailsFormHandler=function(b,c){this.parent(b,c);a('[id^="generatePassword"]',b).click(this.callbackWrapper(this.setGenerateRandom));a('[id^="generatePassword"]',b).attr("checked")&&this.setGenerateRandom('[id^="generatePassword"]')};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.settings.user.form.UserDetailsFormHandler,a.pkp.controllers.form.UserFormHandler);
a.pkp.controllers.grid.settings.user.form.UserDetailsFormHandler.prototype.submitForm=function(b,c){var d=this.getHtmlElement();a(":password",d).removeAttr("disabled");this.parent("submitForm",b,c)};a.pkp.controllers.grid.settings.user.form.UserDetailsFormHandler.prototype.setGenerateRandom=function(b){var c=a(b);b=this.getHtmlElement();if(c.prop("checked")){c="********";var d="disabled"}else d=c="";a(":password",b).prop("disabled",d).val(c);a('[id^="sendNotify"]',b).attr("disabled",d).prop("checked",
d)}})(jQuery);(function(a){a.pkp.controllers.grid.settings.roles=a.pkp.controllers.grid.settings.roles||{form:{}};a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler=function(b,c){var d=a('[id^="roleId"]',b);this.parent(b,c);c.selfRegistrationRoleIds&&(this.selfRegistrationRoleIds_=c.selfRegistrationRoleIds);c.recommendOnlyRoleIds&&(this.recommendOnlyRoleIds_=c.recommendOnlyRoleIds);c.permitSettingsRoleIds&&(this.permitSettingsRoleIds_=c.permitSettingsRoleIds);c.mySettingsAccessUserGroupIds&&(this.mySettingsAccessUserGroupIds_=
c.mySettingsAccessUserGroupIds);c.notChangeMetadataEditPermissionRoles&&(this.notChangeMetadataEditPermissionRoles_=c.notChangeMetadataEditPermissionRoles);this.roleForbiddenStages_=c.roleForbiddenStagesJSON.content;this.stagesSelector_=c.stagesSelector;this.updatePermitSelfRegistration(d.val());this.updatePermitMetadataEdit(d.val(),!1);this.updatePermitSettings(d.val(),!1);this.updateStageOptions(d.val());this.updateRecommendOnly(d.val());d.change(this.callbackWrapper(this.changeRoleId))};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler,
a.pkp.controllers.form.AjaxFormHandler);a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.selfRegistrationRoleIds_=null;a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.permitSettingsRoleIds_=null;a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.mySettingsAccessUserGroupIds_=null;a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.roleForbiddenStages_=null;a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.stagesSelector_=
null;a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.notChangeMetadataEditPermissionRoles_=null;a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.changeRoleId=function(b){b=a(b).val();this.updatePermitSelfRegistration(b);this.updatePermitMetadataEdit(b,!0);this.updatePermitSettings(b,!0);this.updateStageOptions(b);this.updateRecommendOnly(b)};a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.updatePermitSelfRegistration=function(b){var c=
a('[id^="permitSelfRegistration"]');this.getHtmlElement();var d,e=!1;for(d=0;d<this.selfRegistrationRoleIds_.length;d++)this.selfRegistrationRoleIds_[d]==b&&(e=!0);e?c.removeAttr("disabled"):(c.attr("disabled","disabled"),c.removeAttr("checked"))};a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.updatePermitSettings=function(b){var c=a('[id^="permitSettings"]'),d=this.getHtmlElement(),e=a('[id="userGroupId"]',d);d=!1;var f=1==this.mySettingsAccessUserGroupIds_.length&&this.mySettingsAccessUserGroupIds_[0]==
e.attr("value");for(e=0;e<this.selfRegistrationRoleIds_.length;e++)this.permitSettingsRoleIds_[e]==b&&(d=!0);d?f?c.attr("disabled","disabled"):c.removeAttr("disabled"):(c.attr("disabled","disabled"),c.removeAttr("checked"))};a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.updatePermitMetadataEdit=function(b,c){var d,e=a('[id^="permitMetadataEdit"]'),f=!1;for(d=0;d<this.notChangeMetadataEditPermissionRoles_.length;d++)this.notChangeMetadataEditPermissionRoles_[d]==b&&(f=!0);
f?(e.attr("disabled","disabled"),e.attr("checked","checked"),e.prop("checked","checked")):(e.removeAttr("disabled"),c&&e.removeAttr("checked"))};a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.updateStageOptions=function(b){var c=this.getHtmlElement(),d=c.find("#userGroupStageContainer"),e=a(this.stagesSelector_,c).filter("input"),f;e.removeAttr("disabled");if(void 0!=this.roleForbiddenStages_[b])for(f=0;f<this.roleForbiddenStages_[b].length;f++){var k=this.roleForbiddenStages_[b][f];
e.filter('input[value="'+k+'"]').attr("disabled","disabled")}0==c.find("input[id^='assignedStages-']:enabled").length?(d.hide("slow"),a("#showTitle").attr("disabled","disabled")):(d.show("slow"),a("#showTitle").removeAttr("disabled"))};a.pkp.controllers.grid.settings.roles.form.UserGroupFormHandler.prototype.updateRecommendOnly=function(b){var c=a("[id^='recommendOnly']",this.getHtmlElement()),d,e=!1;for(d=0;d<this.recommendOnlyRoleIds_.length;d++)this.recommendOnlyRoleIds_[d]==b&&(e=!0);e?c.removeAttr("disabled"):
(c.attr("disabled","disabled"),c.removeAttr("checked"))}})(jQuery);(function(a){a.pkp.controllers.grid.notifications=a.pkp.controllers.grid.notifications||{};a.pkp.controllers.grid.notifications.NotificationsGridHandler=function(b,c){b.find('a[id*="markNew"]').click(this.callbackWrapper(this.markNewHandler_));b.find('a[id*="markRead"]').click(this.callbackWrapper(this.markReadHandler_));b.find('a[id*="deleteNotification"]').click(this.callbackWrapper(this.deleteHandler_));this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.notifications.NotificationsGridHandler,
a.pkp.controllers.grid.GridHandler);a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.csrfToken_=null;a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.markNewUrl_=null;a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.markReadUrl_=null;a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.deleteUrl_=null;a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.initialize=function(b){this.markNewUrl_=
b.markNewUrl;this.markReadUrl_=b.markReadUrl;this.deleteUrl_=b.deleteUrl;this.csrfToken_=b.csrfToken;this.parent("initialize",b)};a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.getSelectedNotifications_=function(){var b=[];this.getHtmlElement().find("input:checkbox:checked").each(function(){b.push(a(this).val())});return b};a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.markNewHandler_=function(b,c){a.post(this.markNewUrl_,{selectedElements:this.getSelectedNotifications_(),
csrfToken:this.csrfToken_},this.callbackWrapper(this.responseHandler_,null),"json");return!1};a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.markReadHandler_=function(b,c){a.post(this.markReadUrl_,{selectedElements:this.getSelectedNotifications_(),csrfToken:this.csrfToken_},this.callbackWrapper(this.responseHandler_,null),"json");return!1};a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.deleteHandler_=function(b,c){a.post(this.deleteUrl_,{selectedElements:this.getSelectedNotifications_(),
csrfToken:this.csrfToken_},this.callbackWrapper(this.responseHandler_,null),"json");return!1};a.pkp.controllers.grid.notifications.NotificationsGridHandler.prototype.responseHandler_=function(b,c){b=this.getFetchExtraParams();b.selectedNotificationIds=c.content;this.setFetchExtraParams(b);this.handleJson(c)}})(jQuery);jQuery.pkp.controllers.informationCenter=jQuery.pkp.controllers.informationCenter||{};
(function(a){a.pkp.controllers.informationCenter.NotesHandler=function(b,c){this.parent(b,c);this.bind("noteAdded",this.handleRefreshNoteList);this.bind("noteDeleted",this.handleRefreshNoteList)};a.pkp.classes.Helper.inherits(a.pkp.controllers.informationCenter.NotesHandler,a.pkp.classes.Handler);a.pkp.controllers.informationCenter.NotesHandler.prototype.handleRefreshNoteList=function(b,c,d){a(".pkp_modal").first().scrollTop(0);this.replaceWith(d)}})(jQuery);(function(a){a.pkp.controllers.dashboard=a.pkp.controllers.dashboard||{form:{}};a.pkp.controllers.dashboard.form.DashboardTaskFormHandler=function(b,c){this.parent(b,c);this.singleContextSubmissionUrl_=c.singleContextSubmissionUrl;a("#singleContext",b).click(this.callbackWrapper(this.startSingleContextSubmission_));a("#multipleContext",b).change(this.callbackWrapper(this.startMultipleContextSubmission_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.dashboard.form.DashboardTaskFormHandler,a.pkp.controllers.form.FormHandler);
a.pkp.controllers.dashboard.form.DashboardTaskFormHandler.prototype.singleContextSubmissionUrl_=null;a.pkp.controllers.dashboard.form.DashboardTaskFormHandler.prototype.startSingleContextSubmission_=function(){window.location.href=this.singleContextSubmissionUrl_};a.pkp.controllers.dashboard.form.DashboardTaskFormHandler.prototype.startMultipleContextSubmission_=function(){var b=this.getHtmlElement().find("#multipleContext").val();0!=b&&(window.location.href=b)}})(jQuery);(function(a){a.pkp.pages=a.pkp.pages||{header:{}};a.pkp.pages.header.HeaderHandler=function(b,c){this.options_=c;this.parent(b,c)};a.pkp.classes.Helper.inherits(a.pkp.pages.header.HeaderHandler,a.pkp.classes.Handler);a.pkp.pages.header.HeaderHandler.prototype.options_=null})(jQuery);(function(a){a.pkp.pages.header.TasksHandler=function(b,c){this.options_=c;this.parent(b,c);a("#notificationsToggle").click(this.callbackWrapper(this.appendToggleIndicator_));this.bind("updateUnreadNotificationsCount",this.fetchUnreadNotificationsCountHandler_)};a.pkp.classes.Helper.inherits(a.pkp.pages.header.TasksHandler,a.pkp.classes.Handler);a.pkp.pages.header.TasksHandler.prototype.options_=null;a.pkp.pages.header.TasksHandler.prototype.appendToggleIndicator_=function(b,c){b=this.getHtmlElement();
var d=b.find("#notificationsPopover"),e=b.find("#notificationsToggle");d.toggle();e.toggleClass("expandedIndicator");e.hasClass("expandedIndicator")?(this.trigger("callWhenClickOutside",[{container:b,callback:this.callbackWrapper(this.appendToggleIndicator_)}]),setTimeout(this.callbackWrapper(this.setPopoverSize_),500)):(d.css("height","").css("overflow-y",""),c&&this.trigger("callWhenClickOutside",[{container:b,clear:!0}]))};a.pkp.pages.header.TasksHandler.prototype.setPopoverSize_=function(){var b=
this.getHtmlElement().find("#notificationsPopover"),c=a(window).height()-60;c<b.height()&&b.height(c).css("overflow-y","scroll")};a.pkp.pages.header.TasksHandler.prototype.fetchUnreadNotificationsCountHandler_=function(b,c){a.get(this.options_.fetchUnreadNotificationsCountUrl,this.callbackWrapper(this.updateUnreadNotificationsCountHandler_),"json")};a.pkp.pages.header.TasksHandler.prototype.updateUnreadNotificationsCountHandler_=function(b,c){b=this.getHtmlElement().find("#unreadNotificationCount");
b.html(c.content);"0"==c.content?b.removeClass("hasTasks"):b.addClass("hasTasks")}})(jQuery);(function(a){a.pkp.controllers.grid.users=a.pkp.controllers.grid.users||{};a.pkp.controllers.grid.users.UserGridHandler=function(b,c){this.parent(b,c);this.bindGlobal("userMerged",function(){this.trigger("modalFinished");this.refreshGridHandler()});this.bindGlobal("userGroupUpdated",function(){this.refreshGridHandler()})};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.users.UserGridHandler,a.pkp.controllers.grid.GridHandler)})(jQuery);(function(a){a.pkp.controllers.grid.users.reviewer=a.pkp.controllers.grid.users.reviewer||{};a.pkp.controllers.grid.users.reviewer.AdvancedReviewerSearchHandler=function(b,c){this.parent(b,c);b.find(".button").button();var d=this;pkp.eventBus.$on("selected:reviewer",function(e){d.handleReviewerAssign_(b,c,e)});a("#regularReviewerForm").hide();this.bind("refreshForm",this.handleRefresh_);b.find("input#reviewerId").val()&&(this.initializeTinyMCE(),this.handleReviewerAssign_(b,c,{id:b.find("input#reviewerId").val(),
fullName:c.reviewerName}))};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.users.reviewer.AdvancedReviewerSearchHandler,a.pkp.classes.Handler);a.pkp.controllers.grid.users.reviewer.AdvancedReviewerSearchHandler.prototype.handleRefresh_=function(b,c,d){d&&this.replaceWith(d)};a.pkp.controllers.grid.users.reviewer.AdvancedReviewerSearchHandler.prototype.handleReviewerAssign_=function(b,c,d){a("#reviewerId").val(d.id);a('[id^="selectedReviewerName"]').text(d.fullName);a("#searchGridAndButton").hide();
a("#regularReviewerForm").show();var e=a('#reviewerFormFooter [name="personalMessage"]');if(!e.val()){b=a('#reviewerFormFooter input[name="template"]');var f=a('#reviewerFormFooter select[name="template"]');var k=window.tinyMCE.EditorManager.get(e.attr("id"));e="";c.lastRoundReviewerIds.includes(d.id)?(e="REVIEW_REQUEST_SUBSEQUENT",f.find('[value="REVIEW_REQUEST"]').remove()):(e="REVIEW_REQUEST",f.find('[value="REVIEW_REQUEST_SUBSEQUENT"]').remove());var g=c.reviewerMessages[e];k.setContent(g);b.val(e);
k.on("activate",function(){k.getContent().length||k.setContent(g)});f.find('[value="'+e+'"]').prop("selected",!0)}}})(jQuery);(function(a){a.pkp.controllers.grid.users.reviewer.form=a.pkp.controllers.grid.users.reviewer.form||{};a.pkp.controllers.grid.users.reviewer.form.EditReviewFormHandler=function(b,c){this.parent(b,c);b.change(this.callbackWrapper(this.handleFormChange));this.bind("urlInDivLoaded",this.handleFileListLoad_)};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.users.reviewer.form.EditReviewFormHandler,a.pkp.controllers.form.UserFormHandler);a.pkp.controllers.grid.users.reviewer.form.EditReviewFormHandler.prototype.handleFormChange=
function(){this.getHtmlElement().find('input[name="selectedFiles[]"]:checked').length?this.hideWarning():this.showWarning()};a.pkp.controllers.grid.users.reviewer.form.EditReviewFormHandler.prototype.hideWarning=function(){this.getHtmlElement().find("#noFilesWarning").hide(250)};a.pkp.controllers.grid.users.reviewer.form.EditReviewFormHandler.prototype.showWarning=function(){this.getHtmlElement().find("#noFilesWarning").show(250)};a.pkp.controllers.grid.users.reviewer.form.EditReviewFormHandler.prototype.handleFileListLoad_=
function(b,c,d){this.getHtmlElement().change()}})(jQuery);(function(a){a.pkp.controllers.grid.users.reviewer.form.AddReviewerFormHandler=function(b,c){this.parent(b,c);c.templateUrl&&(this.templateUrl_=c.templateUrl);b.find("#template").change(this.callbackWrapper(this.selectTemplateHandler_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.users.reviewer.form.AddReviewerFormHandler,a.pkp.controllers.grid.users.reviewer.form.EditReviewFormHandler);a.pkp.controllers.grid.users.reviewer.form.AddReviewerFormHandler.prototype.templateUrl_=null;a.pkp.controllers.grid.users.reviewer.form.AddReviewerFormHandler.prototype.showWarning=
function(){this.parent("showWarning");this.getHtmlElement().find("#reviewerFormFooter").trigger("expandFileList")};a.pkp.controllers.grid.users.reviewer.form.AddReviewerFormHandler.prototype.selectTemplateHandler_=function(b,c){b=this.getHtmlElement();a.post(this.templateUrl_,b.find("#template").serialize(),this.callbackWrapper(this.updateTemplate),"json")};a.pkp.controllers.grid.users.reviewer.form.AddReviewerFormHandler.prototype.updateTemplate=function(b,c){b=this.getHtmlElement();c=this.handleJson(c);
b=b.find('textarea[name="personalMessage"]');b=tinyMCE.EditorManager.get(b.attr("id"));!1!==c&&""!==c.content&&b.setContent(c.content);return c.status}})(jQuery);(function(a){a.pkp.controllers.grid.users.stageParticipant=a.pkp.controllers.grid.users.stageParticipant||{};a.pkp.controllers.grid.users.stageParticipant.StageParticipantGridHandler=function(b,c){this.parent(b,c);this.bind("dataChanged",function(){this.refreshGridHandler();a("#submissionEditorDecisionsDiv,#copyeditingEditorDecisionsDiv,[id^=reviewDecisionsDiv]").each(function(){a.pkp.classes.Handler.getHandler(a(this)).reload()})})};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.users.stageParticipant.StageParticipantGridHandler,
a.pkp.controllers.grid.CategoryGridHandler)})(jQuery);(function(a){a.pkp.controllers.grid.users.stageParticipant.form=a.pkp.controllers.grid.users.stageParticipant.form||{};a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler=function(b,c){this.parent(b,c);c.templateUrl&&(this.templateUrl_=c.templateUrl);c.possibleRecommendOnlyUserGroupIds&&(this.possibleRecommendOnlyUserGroupIds_=c.possibleRecommendOnlyUserGroupIds);c.recommendOnlyUserGroupIds&&(this.recommendOnlyUserGroupIds_=c.recommendOnlyUserGroupIds);c.notChangeMetadataEditPermissionRoles&&
(this.notChangeMetadataEditPermissionRoles_=c.notChangeMetadataEditPermissionRoles);c.permitMetadataEditUserGroupIds&&(this.permitMetadataEditUserGroupIds_=c.permitMetadataEditUserGroupIds);c.anonymousReviewerIds&&(this.anonymousReviewerIds_=c.anonymousReviewerIds);c.anonymousReviewerWarning&&(this.anonymousReviewerWarning_=c.anonymousReviewerWarning);c.anonymousReviewerWarningOk&&(this.anonymousReviewerWarningOk_=c.anonymousReviewerWarningOk);a("input[name='userGroupId'], input[name='userIdSelected']",
b).change(this.callbackWrapper(this.updateRecommendOnly));a("input[name='userGroupId'], input[name='userIdSelected']",b).change(this.callbackWrapper(this.updateSubmissionMetadataEditPermitOption));a("input[name='userIdSelected']",b).change(this.callbackWrapper(this.maybeTriggerReviewerWarning));b.find("#template").change(this.callbackWrapper(this.selectTemplateHandler_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler,a.pkp.controllers.form.AjaxFormHandler);
a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.templateUrl_=null;a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.anonymousReviewerIds_=null;a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.anonymousReviewerWarning_=null;a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.anonymousReviewerWarningOk_=null;a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.notChangeMetadataEditPermissionRoles_=
null;a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.permitMetadataEditUserGroupIds_=null;a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.selectTemplateHandler_=function(b,c){b=this.getHtmlElement();a.post(this.templateUrl_,b.find("#template").serialize(),this.callbackWrapper(this.updateTemplate),"json")};a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.updateTemplate=function(b,
c){var d=this.getHtmlElement();b=this.handleJson(c);c=c.content;d=d.find('textarea[name="message"]');var e=tinyMCE.EditorManager.get(d.attr("id"));c.variables&&d.attr("data-variables",JSON.stringify(c.variables));e.setContent(c.body);return b.status};a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.updateRecommendOnly=function(b,c){var d=this.getHtmlElement(),e=d.find("input[name='userGroupId']");c=d.find("input[id^='recommendOnly']");d=d.find(".recommendOnlyWrapper");
e=e.val();if("userGroupId"==a(b).prop("name"))c.attr("disabled","disabled"),c.removeAttr("checked"),c.prop("checked",!1),d.hide();else if("userIdSelected"==a(b).prop("name")&&!d.is(":visible"))for(b=0;b<this.possibleRecommendOnlyUserGroupIds_.length;b++)if(this.possibleRecommendOnlyUserGroupIds_[b]==e){c.removeAttr("disabled");d.show();for(b=0;b<this.recommendOnlyUserGroupIds_.length;b++)if(this.recommendOnlyUserGroupIds_[b]==e){c.prop("checked",!0);break}break}else c.attr("disabled","disabled"),
d.hide()};a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.maybeTriggerReviewerWarning=function(b,c){!(b=a(b).val())||0>this.anonymousReviewerIds_.indexOf(b)||(b={title:"",okButton:this.anonymousReviewerWarningOk_,cancelButton:!1,dialogText:this.anonymousReviewerWarning_},a('<div id="'+a.pkp.classes.Helper.uuid()+'" class="pkp_modal pkpModalWrapper" tabindex="-1"></div>').pkpHandler("$.pkp.controllers.modal.ConfirmationModalHandler",b))};a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.updateSubmissionMetadataEditPermitOption=
function(b,c){var d=this.getHtmlElement(),e=d.find("input[name='userGroupId']");c=d.find("input[id^='canChangeMetadata']");d=d.find(".submissionEditMetadataPermit");var f=!1;e=e.val();if("userGroupId"==a(b).prop("name"))c.attr("disabled","disabled"),c.removeAttr("checked"),d.hide();else if("userIdSelected"==a(b).prop("name")&&!d.is(":visible")){for(b=0;b<this.notChangeMetadataEditPermissionRoles_.length;b++)if(this.notChangeMetadataEditPermissionRoles_[b]==e){f=!0;break}if(f)c.attr("disabled","disabled"),
d.hide();else for(c.removeAttr("disabled"),d.show(),b=0;b<this.permitMetadataEditUserGroupIds_.length;b++)if(this.permitMetadataEditUserGroupIds_[b]==e){c.prop("checked",!0);break}}};a.pkp.controllers.grid.users.stageParticipant.form.StageParticipantNotifyHandler.prototype.handleResponse=function(b,c){var d=a("#queriesGrid .pkp_controllers_grid");a.pkp.classes.Handler.hasHandler(d)&&a.pkp.classes.Handler.getHandler(d).trigger("dataChanged");return this.parent("handleResponse",b,c)}})(jQuery);(function(a){a.pkp.controllers.grid.users.stageParticipant.form.AddParticipantFormHandler=function(b,c){this.parent(b,c);a("select[name^='filterUserGroupId']",b).change(this.callbackWrapper(this.addUserGroupId));b.parent().click(function(d){a(d.target).is('input[name="userId"]')&&(d=a("input[name='userId']:checked").val(),a("input[name='userIdSelected']").val(d).trigger("change"))});this.addUserGroupId()};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.users.stageParticipant.form.AddParticipantFormHandler,
a.pkp.controllers.form.ClientFormHandler);a.pkp.controllers.grid.users.stageParticipant.form.AddParticipantFormHandler.prototype.addUserGroupId=function(){var b=this.getHtmlElement().find("select[name^='filterUserGroupId']").val();a("input[name='userGroupId']").val(b).trigger("change")}})(jQuery);(function(a){a.pkp.pages.reviewer=a.pkp.pages.reviewer||{};a.pkp.pages.reviewer.ReviewerTabHandler=function(b,c){this.parent(b,c);this.reviewStep_=c.reviewStep;this.bind("setStep",this.setStepHandler);this.getHtmlElement().tabs("option","disabled",this.getDisabledSteps(this.reviewStep_))};a.pkp.classes.Helper.inherits(a.pkp.pages.reviewer.ReviewerTabHandler,a.pkp.controllers.TabHandler);a.pkp.pages.reviewer.ReviewerTabHandler.prototype.reviewStep_=null;a.pkp.pages.reviewer.ReviewerTabHandler.prototype.setStepHandler=
function(b,c,d){this.getHtmlElement().tabs("option","disabled",this.getDisabledSteps(d));this.getHtmlElement().tabs("option","active",d-1)};a.pkp.pages.reviewer.ReviewerTabHandler.prototype.getDisabledSteps=function(b){switch(b){case 1:return[1,2,3];case 2:return[2,3];case 3:return[3];case 4:return[]}throw Error("Illegal review step number.");}})(jQuery);(function(a){a.pkp.pages.authorDashboard=a.pkp.pages.authorDashboard||{};a.pkp.pages.authorDashboard.SubmissionEmailHandler=function(b,c){this.parent(b,c);b.find('a[id^="submissionEmail"]').click(this.callbackWrapper(this.activateAction))};a.pkp.classes.Helper.inherits(a.pkp.pages.authorDashboard.SubmissionEmailHandler,a.pkp.controllers.linkAction.LinkActionHandler)})(jQuery);(function(a){a.pkp.controllers.grid.preprintGalleys=a.pkp.controllers.grid.preprintGalleys||{};a.pkp.controllers.grid.preprintGalleys.PreprintGalleyGridHandler=function(b,c){this.parent(b,c);b.bind("uploadFile",this.callbackWrapper(this.uploadFileHandler_))};a.pkp.classes.Helper.inherits(a.pkp.controllers.grid.preprintGalleys.PreprintGalleyGridHandler,a.pkp.controllers.grid.GridHandler);a.pkp.controllers.grid.preprintGalleys.PreprintGalleyGridHandler.prototype.uploadFileHandler_=function(b,c,d){var e=
'a[id^="component-grid-preprintgalleys-preprintgalleygrid-row-'+d+'-addFile-button-"]';a.when(a(e)).then(function(){a(function(){a(e).click()})})}})(jQuery);(function(a){a.fn.pkpHandler=function(b,c){this.each(function(){var d=a(this);c=c||{};a.pkp.classes.Helper.objectFactory(b,[d,c])});return this};a.fn.pkpAjaxHtml=function(b,c){var d=this.first();a.ajax({url:b,dataType:"json",success:function(e){d.find("#loading").hide();!0===e.status?(e.content&&d.html(e.content),c&&c()):(d.trigger("ajaxHtmlError",e.content),alert(e.content))},error:function(){alert("Failed Ajax request or invalid JSON returned.")}});d.html("<div id='loading' class='throbber'></div>");
return this}})(jQuery);
